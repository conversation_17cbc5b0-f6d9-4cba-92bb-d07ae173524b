{"version": 3, "file": "init-Dmth1JHB.js", "sources": ["../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/init.js"], "sourcesContent": ["export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n"], "names": ["initRange", "domain", "range", "initInterpolator", "interpolator"], "mappings": "AAAO,SAASA,EAAUC,EAAQC,EAAO,CACvC,OAAQ,UAAU,OAAM,CACtB,IAAK,GAAG,MACR,IAAK,GAAG,KAAK,MAAMD,CAAM,EAAG,MAC5B,QAAS,KAAK,MAAMC,CAAK,EAAE,OAAOD,CAAM,EAAG,KAC5C,CACD,OAAO,IACT,CAEO,SAASE,EAAiBF,EAAQG,EAAc,CACrD,OAAQ,UAAU,OAAM,CACtB,IAAK,GAAG,MACR,IAAK,GAAG,CACF,OAAOH,GAAW,WAAY,KAAK,aAAaA,CAAM,EACrD,KAAK,MAAMA,CAAM,EACtB,KACD,CACD,QAAS,CACP,KAAK,OAAOA,CAAM,EACd,OAAOG,GAAiB,WAAY,KAAK,aAAaA,CAAY,EACjE,KAAK,MAAMA,CAAY,EAC5B,KACD,CACF,CACD,OAAO,IACT", "x_google_ignoreList": [0]}