import{a as Ke}from"./Upload-D4uXt6Nz.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{B as Me}from"./BlockLabel-3KxTaaiM.js";import{V as Qe}from"./Video-fsmLZWjA.js";import"./index-B7J2Z2jS.js";import{S as Re}from"./SelectSource-CVJruJ8L.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./Image-CnqB5dbD.js";/* empty css                                                   */import{W as Xe}from"./ImageUploader-CGsNpf2-.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";/* empty css                                              */import{p as be}from"./Video-DtShVFLe.js";import{l as Il,a as jl}from"./Video-DtShVFLe.js";import{P as Ye,V as Ze}from"./VideoPreview-DBQL_gko.js";import{default as Pl}from"./Example-B74kJcnr.js";import{B as ke}from"./Block-CJdXVpa7.js";import{U as ye}from"./UploadText-DtO3Yo94.js";import{S as ze}from"./index-B1FJGuzG.js";/* empty css                                             */import"./prism-python-MMh3z1bK.js";import"./svelte/svelte.js";import"./file-url-DoxvUUVV.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";import"./Image-Bsh8Umrh.js";import"./IconButtonWrapper--EIOWuEM.js";import"./FullscreenButton-jgOGhOHz.js";import"./utils-Gtzs_Zla.js";import"./DropdownArrow-DYWFcSFn.js";import"./Square-oAGqOwsh.js";import"./index-CEGzm7H5.js";import"./StreamingBar-JqJtcvLZ.js";import"./hls-CnVhpNcu.js";import"./Empty-ZqppqzTN.js";import"./ShareButton-BuIiIMKb.js";import"./Community-Dw1micSV.js";import"./utils-BsGrhMNe.js";import"./Download-DVtk-Jv3.js";import"./DownloadLink-QIttOhoR.js";import"./Trim-JQYgj7Jd.js";import"./Play-B0Q0U1Qz.js";import"./Undo-DCjBnnSO.js";import"./ModifyUpload-5lgGIpMc.js";import"./Edit-BpRIf5rU.js";const{SvelteComponent:$e,add_flush_callback:ae,append:ue,attr:M,bind:re,binding_callbacks:_e,bubble:U,check_outros:ce,create_component:Q,create_slot:xe,destroy_component:R,detach:J,element:y,empty:et,flush:v,get_all_dirty_from_scope:tt,get_slot_changes:lt,group_outros:de,init:nt,insert:O,mount_component:X,noop:fe,safe_not_equal:Se,set_data:we,space:he,text:pe,transition_in:z,transition_out:S,update_slot_base:ot}=window.__gradio__svelte__internal,{createEventDispatcher:it}=window.__gradio__svelte__internal;function st(t){let e,n=(t[0].orig_name||t[0].url)+"",l,o,i,f=be(t[0].size)+"",_;return{c(){e=y("div"),l=pe(n),o=he(),i=y("div"),_=pe(f),M(e,"class","file-name svelte-14jis2k"),M(i,"class","file-size svelte-14jis2k")},m(s,h){O(s,e,h),ue(e,l),O(s,o,h),O(s,i,h),ue(i,_)},p(s,h){h[0]&1&&n!==(n=(s[0].orig_name||s[0].url)+"")&&we(l,n),h[0]&1&&f!==(f=be(s[0].size)+"")&&we(_,f)},i:fe,o:fe,d(s){s&&(J(e),J(o),J(i))}}}function at(t){let e=t[0]?.url,n,l,o=ve(t);return{c(){o.c(),n=et()},m(i,f){o.m(i,f),O(i,n,f),l=!0},p(i,f){f[0]&1&&Se(e,e=i[0]?.url)?(de(),S(o,1,1,fe),ce(),o=ve(i),o.c(),z(o,1),o.m(n.parentNode,n)):o.p(i,f)},i(i){l||(z(o),l=!0)},o(i){S(o),l=!1},d(i){i&&J(n),o.d(i)}}}function ut(t){let e,n,l,o;const i=[_t,rt],f=[];function _(s,h){return s[1]==="upload"?0:s[1]==="webcam"?1:-1}return~(n=_(t))&&(l=f[n]=i[n](t)),{c(){e=y("div"),l&&l.c(),M(e,"class","upload-container svelte-14jis2k")},m(s,h){O(s,e,h),~n&&f[n].m(e,null),o=!0},p(s,h){let a=n;n=_(s),n===a?~n&&f[n].p(s,h):(l&&(de(),S(f[a],1,1,()=>{f[a]=null}),ce()),~n?(l=f[n],l?l.p(s,h):(l=f[n]=i[n](s),l.c()),z(l,1),l.m(e,null)):l=null)},i(s){o||(z(l),o=!0)},o(s){S(l),o=!1},d(s){s&&J(e),~n&&f[n].d()}}}function ve(t){let e,n;return e=new Ye({props:{upload:t[15],root:t[11],interactive:!0,autoplay:t[10],src:t[0].url,subtitle:t[3]?.url,is_stream:!1,mirror:t[8].mirror&&t[1]==="webcam",label:t[5],handle_change:t[23],handle_reset_value:t[13],loop:t[17],value:t[0],i18n:t[12],show_download_button:t[6],handle_clear:t[22],has_change_history:t[19]}}),e.$on("play",t[32]),e.$on("pause",t[33]),e.$on("stop",t[34]),e.$on("end",t[35]),e.$on("error",t[36]),{c(){Q(e.$$.fragment)},m(l,o){X(e,l,o),n=!0},p(l,o){const i={};o[0]&32768&&(i.upload=l[15]),o[0]&2048&&(i.root=l[11]),o[0]&1024&&(i.autoplay=l[10]),o[0]&1&&(i.src=l[0].url),o[0]&8&&(i.subtitle=l[3]?.url),o[0]&258&&(i.mirror=l[8].mirror&&l[1]==="webcam"),o[0]&32&&(i.label=l[5]),o[0]&8192&&(i.handle_reset_value=l[13]),o[0]&131072&&(i.loop=l[17]),o[0]&1&&(i.value=l[0]),o[0]&4096&&(i.i18n=l[12]),o[0]&64&&(i.show_download_button=l[6]),o[0]&524288&&(i.has_change_history=l[19]),e.$set(i)},i(l){n||(z(e.$$.fragment,l),n=!0)},o(l){S(e.$$.fragment,l),n=!1},d(l){R(e,l)}}}function rt(t){let e,n;return e=new Xe({props:{root:t[11],mirror_webcam:t[8].mirror,webcam_constraints:t[8].constraints,include_audio:t[9],mode:"video",i18n:t[12],upload:t[15],stream_every:1}}),e.$on("error",t[29]),e.$on("capture",t[24]),e.$on("start_recording",t[30]),e.$on("stop_recording",t[31]),{c(){Q(e.$$.fragment)},m(l,o){X(e,l,o),n=!0},p(l,o){const i={};o[0]&2048&&(i.root=l[11]),o[0]&256&&(i.mirror_webcam=l[8].mirror),o[0]&256&&(i.webcam_constraints=l[8].constraints),o[0]&512&&(i.include_audio=l[9]),o[0]&4096&&(i.i18n=l[12]),o[0]&32768&&(i.upload=l[15]),e.$set(i)},i(l){n||(z(e.$$.fragment,l),n=!0)},o(l){S(e.$$.fragment,l),n=!1},d(l){R(e,l)}}}function _t(t){let e,n,l,o;function i(s){t[26](s)}function f(s){t[27](s)}let _={filetype:"video/x-m4v,video/*",max_file_size:t[14],root:t[11],upload:t[15],stream_handler:t[16],aria_label:t[12]("video.drop_to_upload"),$$slots:{default:[ft]},$$scope:{ctx:t}};return t[18]!==void 0&&(_.dragging=t[18]),t[2]!==void 0&&(_.uploading=t[2]),e=new Ke({props:_}),_e.push(()=>re(e,"dragging",i)),_e.push(()=>re(e,"uploading",f)),e.$on("load",t[21]),e.$on("error",t[28]),{c(){Q(e.$$.fragment)},m(s,h){X(e,s,h),o=!0},p(s,h){const a={};h[0]&16384&&(a.max_file_size=s[14]),h[0]&2048&&(a.root=s[11]),h[0]&32768&&(a.upload=s[15]),h[0]&65536&&(a.stream_handler=s[16]),h[0]&4096&&(a.aria_label=s[12]("video.drop_to_upload")),h[1]&128&&(a.$$scope={dirty:h,ctx:s}),!n&&h[0]&262144&&(n=!0,a.dragging=s[18],ae(()=>n=!1)),!l&&h[0]&4&&(l=!0,a.uploading=s[2],ae(()=>l=!1)),e.$set(a)},i(s){o||(z(e.$$.fragment,s),o=!0)},o(s){S(e.$$.fragment,s),o=!1},d(s){R(e,s)}}}function ft(t){let e;const n=t[25].default,l=xe(n,t,t[38],null);return{c(){l&&l.c()},m(o,i){l&&l.m(o,i),e=!0},p(o,i){l&&l.p&&(!e||i[1]&128)&&ot(l,n,o,o[38],e?lt(n,o[38],i,null):tt(o[38]),null)},i(o){e||(z(l,o),e=!0)},o(o){S(l,o),e=!1},d(o){l&&l.d(o)}}}function ht(t){let e,n,l,o,i,f,_,s,h;e=new Me({props:{show_label:t[7],Icon:Qe,label:t[5]||"Video"}});const a=[ut,at,st],c=[];function p(d,w){return d[0]===null||d[0].url===void 0?0:d[0]?.url?1:d[0].size?2:-1}~(o=p(t))&&(i=c[o]=a[o](t));function m(d){t[37](d)}let P={sources:t[4],handle_clear:t[22]};return t[1]!==void 0&&(P.active_source=t[1]),_=new Re({props:P}),_e.push(()=>re(_,"active_source",m)),{c(){Q(e.$$.fragment),n=he(),l=y("div"),i&&i.c(),f=he(),Q(_.$$.fragment),M(l,"data-testid","video"),M(l,"class","video-container svelte-14jis2k")},m(d,w){X(e,d,w),O(d,n,w),O(d,l,w),~o&&c[o].m(l,null),ue(l,f),X(_,l,null),h=!0},p(d,w){const V={};w[0]&128&&(V.show_label=d[7]),w[0]&32&&(V.label=d[5]||"Video"),e.$set(V);let B=o;o=p(d),o===B?~o&&c[o].p(d,w):(i&&(de(),S(c[B],1,1,()=>{c[B]=null}),ce()),~o?(i=c[o],i?i.p(d,w):(i=c[o]=a[o](d),i.c()),z(i,1),i.m(l,f)):i=null);const I={};w[0]&16&&(I.sources=d[4]),!s&&w[0]&2&&(s=!0,I.active_source=d[1],ae(()=>s=!1)),_.$set(I)},i(d){h||(z(e.$$.fragment,d),z(i),z(_.$$.fragment,d),h=!0)},o(d){S(e.$$.fragment,d),S(i),S(_.$$.fragment,d),h=!1},d(d){d&&(J(n),J(l)),R(e,d),~o&&c[o].d(),R(_)}}}function ct(t,e,n){let{$$slots:l={},$$scope:o}=e,{value:i=null}=e,{subtitle:f=null}=e,{sources:_=["webcam","upload"]}=e,{label:s=void 0}=e,{show_download_button:h=!1}=e,{show_label:a=!0}=e,{webcam_options:c}=e,{include_audio:p}=e,{autoplay:m}=e,{root:P}=e,{i18n:d}=e,{active_source:w="webcam"}=e,{handle_reset_value:V=()=>{}}=e,{max_file_size:B=null}=e,{upload:I}=e,{stream_handler:A}=e,{loop:g}=e,{uploading:E=!1}=e,F=!1;const k=it();function Y({detail:u}){n(0,i=u),k("change",u),k("upload",u)}function G(){n(0,i=null),k("change",null),k("clear")}function T(u){n(19,F=!0),k("change",u)}function H({detail:u}){k("change",u)}let q=!1;function K(u){q=u,n(18,q)}function C(u){E=u,n(2,E)}const $=({detail:u})=>k("error",u);function Z(u){U.call(this,t,u)}function x(u){U.call(this,t,u)}function ee(u){U.call(this,t,u)}function te(u){U.call(this,t,u)}function le(u){U.call(this,t,u)}function ne(u){U.call(this,t,u)}function oe(u){U.call(this,t,u)}function ie(u){U.call(this,t,u)}function se(u){w=u,n(1,w)}return t.$$set=u=>{"value"in u&&n(0,i=u.value),"subtitle"in u&&n(3,f=u.subtitle),"sources"in u&&n(4,_=u.sources),"label"in u&&n(5,s=u.label),"show_download_button"in u&&n(6,h=u.show_download_button),"show_label"in u&&n(7,a=u.show_label),"webcam_options"in u&&n(8,c=u.webcam_options),"include_audio"in u&&n(9,p=u.include_audio),"autoplay"in u&&n(10,m=u.autoplay),"root"in u&&n(11,P=u.root),"i18n"in u&&n(12,d=u.i18n),"active_source"in u&&n(1,w=u.active_source),"handle_reset_value"in u&&n(13,V=u.handle_reset_value),"max_file_size"in u&&n(14,B=u.max_file_size),"upload"in u&&n(15,I=u.upload),"stream_handler"in u&&n(16,A=u.stream_handler),"loop"in u&&n(17,g=u.loop),"uploading"in u&&n(2,E=u.uploading),"$$scope"in u&&n(38,o=u.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&262144&&k("drag",q)},[i,w,E,f,_,s,h,a,c,p,m,P,d,V,B,I,A,g,q,F,k,Y,G,T,H,l,K,C,$,Z,x,ee,te,le,ne,oe,ie,se,o]}class dt extends $e{constructor(e){super(),nt(this,e,ct,ht,Se,{value:0,subtitle:3,sources:4,label:5,show_download_button:6,show_label:7,webcam_options:8,include_audio:9,autoplay:10,root:11,i18n:12,active_source:1,handle_reset_value:13,max_file_size:14,upload:15,stream_handler:16,loop:17,uploading:2},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),v()}get subtitle(){return this.$$.ctx[3]}set subtitle(e){this.$$set({subtitle:e}),v()}get sources(){return this.$$.ctx[4]}set sources(e){this.$$set({sources:e}),v()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),v()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),v()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),v()}get webcam_options(){return this.$$.ctx[8]}set webcam_options(e){this.$$set({webcam_options:e}),v()}get include_audio(){return this.$$.ctx[9]}set include_audio(e){this.$$set({include_audio:e}),v()}get autoplay(){return this.$$.ctx[10]}set autoplay(e){this.$$set({autoplay:e}),v()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),v()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),v()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),v()}get handle_reset_value(){return this.$$.ctx[13]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),v()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),v()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),v()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),v()}get loop(){return this.$$.ctx[17]}set loop(e){this.$$set({loop:e}),v()}get uploading(){return this.$$.ctx[2]}set uploading(e){this.$$set({uploading:e}),v()}}const mt=dt,{SvelteComponent:gt,add_flush_callback:bt,assign:Ve,bind:wt,binding_callbacks:pt,check_outros:vt,create_component:W,destroy_component:D,detach:me,empty:kt,flush:b,get_spread_object:Be,get_spread_update:Ie,group_outros:zt,init:St,insert:ge,mount_component:L,safe_not_equal:Vt,space:je,transition_in:j,transition_out:N}=window.__gradio__svelte__internal;function Bt(t){let e,n;return e=new ke({props:{visible:t[4],variant:t[0]===null&&t[23]==="upload"?"dashed":"solid",border_mode:t[26]?"focus":"base",padding:!1,elem_id:t[2],elem_classes:t[3],height:t[9],width:t[10],container:t[11],scale:t[12],min_width:t[13],allow_overflow:!1,$$slots:{default:[Nt]},$$scope:{ctx:t}}}),{c(){W(e.$$.fragment)},m(l,o){L(e,l,o),n=!0},p(l,o){const i={};o[0]&16&&(i.visible=l[4]),o[0]&8388609&&(i.variant=l[0]===null&&l[23]==="upload"?"dashed":"solid"),o[0]&67108864&&(i.border_mode=l[26]?"focus":"base"),o[0]&4&&(i.elem_id=l[2]),o[0]&8&&(i.elem_classes=l[3]),o[0]&512&&(i.height=l[9]),o[0]&1024&&(i.width=l[10]),o[0]&2048&&(i.container=l[11]),o[0]&4096&&(i.scale=l[12]),o[0]&8192&&(i.min_width=l[13]),o[0]&133906914|o[1]&8388608&&(i.$$scope={dirty:o,ctx:l}),e.$set(i)},i(l){n||(j(e.$$.fragment,l),n=!0)},o(l){N(e.$$.fragment,l),n=!1},d(l){D(e,l)}}}function It(t){let e,n;return e=new ke({props:{visible:t[4],variant:t[0]===null&&t[23]==="upload"?"dashed":"solid",border_mode:t[26]?"focus":"base",padding:!1,elem_id:t[2],elem_classes:t[3],height:t[9],width:t[10],container:t[11],scale:t[12],min_width:t[13],allow_overflow:!1,$$slots:{default:[Pt]},$$scope:{ctx:t}}}),{c(){W(e.$$.fragment)},m(l,o){L(e,l,o),n=!0},p(l,o){const i={};o[0]&16&&(i.visible=l[4]),o[0]&8388609&&(i.variant=l[0]===null&&l[23]==="upload"?"dashed":"solid"),o[0]&67108864&&(i.border_mode=l[26]?"focus":"base"),o[0]&4&&(i.elem_id=l[2]),o[0]&8&&(i.elem_classes=l[3]),o[0]&512&&(i.height=l[9]),o[0]&1024&&(i.width=l[10]),o[0]&2048&&(i.container=l[11]),o[0]&4096&&(i.scale=l[12]),o[0]&8192&&(i.min_width=l[13]),o[0]&52674850|o[1]&8388608&&(i.$$scope={dirty:o,ctx:l}),e.$set(i)},i(l){n||(j(e.$$.fragment,l),n=!0)},o(l){N(e.$$.fragment,l),n=!1},d(l){D(e,l)}}}function jt(t){let e,n;return e=new ye({props:{i18n:t[17].i18n,type:"video"}}),{c(){W(e.$$.fragment)},m(l,o){L(e,l,o),n=!0},p(l,o){const i={};o[0]&131072&&(i.i18n=l[17].i18n),e.$set(i)},i(l){n||(j(e.$$.fragment,l),n=!0)},o(l){N(e.$$.fragment,l),n=!1},d(l){D(e,l)}}}function Nt(t){let e,n,l,o,i;const f=[{autoscroll:t[17].autoscroll},{i18n:t[17].i18n},t[1]];let _={};for(let a=0;a<f.length;a+=1)_=Ve(_,f[a]);e=new ze({props:_}),e.$on("clear_status",t[41]);function s(a){t[44](a)}let h={value:t[24],subtitle:t[25],label:t[5],show_label:t[8],show_download_button:t[16],sources:t[6],active_source:t[23],webcam_options:t[19],include_audio:t[20],autoplay:t[14],root:t[7],loop:t[21],handle_reset_value:t[27],i18n:t[17].i18n,max_file_size:t[17].max_file_size,upload:t[42],stream_handler:t[43],$$slots:{default:[jt]},$$scope:{ctx:t}};return t[22]!==void 0&&(h.uploading=t[22]),l=new mt({props:h}),pt.push(()=>wt(l,"uploading",s)),l.$on("change",t[28]),l.$on("drag",t[45]),l.$on("error",t[29]),l.$on("clear",t[46]),l.$on("play",t[47]),l.$on("pause",t[48]),l.$on("upload",t[49]),l.$on("stop",t[50]),l.$on("end",t[51]),l.$on("start_recording",t[52]),l.$on("stop_recording",t[53]),{c(){W(e.$$.fragment),n=je(),W(l.$$.fragment)},m(a,c){L(e,a,c),ge(a,n,c),L(l,a,c),i=!0},p(a,c){const p=c[0]&131074?Ie(f,[c[0]&131072&&{autoscroll:a[17].autoscroll},c[0]&131072&&{i18n:a[17].i18n},c[0]&2&&Be(a[1])]):{};e.$set(p);const m={};c[0]&16777216&&(m.value=a[24]),c[0]&33554432&&(m.subtitle=a[25]),c[0]&32&&(m.label=a[5]),c[0]&256&&(m.show_label=a[8]),c[0]&65536&&(m.show_download_button=a[16]),c[0]&64&&(m.sources=a[6]),c[0]&8388608&&(m.active_source=a[23]),c[0]&524288&&(m.webcam_options=a[19]),c[0]&1048576&&(m.include_audio=a[20]),c[0]&16384&&(m.autoplay=a[14]),c[0]&128&&(m.root=a[7]),c[0]&2097152&&(m.loop=a[21]),c[0]&131072&&(m.i18n=a[17].i18n),c[0]&131072&&(m.max_file_size=a[17].max_file_size),c[0]&131072&&(m.upload=a[42]),c[0]&131072&&(m.stream_handler=a[43]),c[0]&131072|c[1]&8388608&&(m.$$scope={dirty:c,ctx:a}),!o&&c[0]&4194304&&(o=!0,m.uploading=a[22],bt(()=>o=!1)),l.$set(m)},i(a){i||(j(e.$$.fragment,a),j(l.$$.fragment,a),i=!0)},o(a){N(e.$$.fragment,a),N(l.$$.fragment,a),i=!1},d(a){a&&me(n),D(e,a),D(l,a)}}}function Pt(t){let e,n,l,o;const i=[{autoscroll:t[17].autoscroll},{i18n:t[17].i18n},t[1]];let f={};for(let _=0;_<i.length;_+=1)f=Ve(f,i[_]);return e=new ze({props:f}),e.$on("clear_status",t[33]),l=new Ze({props:{value:t[24],subtitle:t[25],label:t[5],show_label:t[8],autoplay:t[14],loop:t[21],show_share_button:t[15],show_download_button:t[16],i18n:t[17].i18n,upload:t[34]}}),l.$on("play",t[35]),l.$on("pause",t[36]),l.$on("stop",t[37]),l.$on("end",t[38]),l.$on("share",t[39]),l.$on("error",t[40]),{c(){W(e.$$.fragment),n=je(),W(l.$$.fragment)},m(_,s){L(e,_,s),ge(_,n,s),L(l,_,s),o=!0},p(_,s){const h=s[0]&131074?Ie(i,[s[0]&131072&&{autoscroll:_[17].autoscroll},s[0]&131072&&{i18n:_[17].i18n},s[0]&2&&Be(_[1])]):{};e.$set(h);const a={};s[0]&16777216&&(a.value=_[24]),s[0]&33554432&&(a.subtitle=_[25]),s[0]&32&&(a.label=_[5]),s[0]&256&&(a.show_label=_[8]),s[0]&16384&&(a.autoplay=_[14]),s[0]&2097152&&(a.loop=_[21]),s[0]&32768&&(a.show_share_button=_[15]),s[0]&65536&&(a.show_download_button=_[16]),s[0]&131072&&(a.i18n=_[17].i18n),s[0]&131072&&(a.upload=_[34]),l.$set(a)},i(_){o||(j(e.$$.fragment,_),j(l.$$.fragment,_),o=!0)},o(_){N(e.$$.fragment,_),N(l.$$.fragment,_),o=!1},d(_){_&&me(n),D(e,_),D(l,_)}}}function qt(t){let e,n,l,o;const i=[It,Bt],f=[];function _(s,h){return s[18]?1:0}return e=_(t),n=f[e]=i[e](t),{c(){n.c(),l=kt()},m(s,h){f[e].m(s,h),ge(s,l,h),o=!0},p(s,h){let a=e;e=_(s),e===a?f[e].p(s,h):(zt(),N(f[a],1,1,()=>{f[a]=null}),vt(),n=f[e],n?n.p(s,h):(n=f[e]=i[e](s),n.c()),j(n,1),n.m(l.parentNode,l))},i(s){o||(j(n),o=!0)},o(s){N(n),o=!1},d(s){s&&me(l),f[e].d(s)}}}function Ct(t,e,n){let{elem_id:l=""}=e,{elem_classes:o=[]}=e,{visible:i=!0}=e,{value:f=null}=e,_=null,{label:s}=e,{sources:h}=e,{root:a}=e,{show_label:c}=e,{loading_status:p}=e,{height:m}=e,{width:P}=e,{container:d=!1}=e,{scale:w=null}=e,{min_width:V=void 0}=e,{autoplay:B=!1}=e,{show_share_button:I=!0}=e,{show_download_button:A}=e,{gradio:g}=e,{interactive:E}=e,{webcam_options:F}=e,{include_audio:k}=e,{loop:Y=!1}=e,{input_ready:G}=e,T=!1,H=null,q=null,K,C=f;const $=()=>{C===null||f===C||n(0,f=C)};let Z=!1;function x({detail:r}){r!=null?n(0,f={video:r,subtitles:null}):n(0,f=null)}function ee({detail:r}){const[Ge,He]=r.includes("Invalid file type")?["warning","complete"]:["error","error"];n(1,p=p||{}),n(1,p.status=He,p),n(1,p.message=r,p),g.dispatch(Ge,r)}const te=()=>g.dispatch("clear_status",p),le=(...r)=>g.client.upload(...r),ne=()=>g.dispatch("play"),oe=()=>g.dispatch("pause"),ie=()=>g.dispatch("stop"),se=()=>g.dispatch("end"),u=({detail:r})=>g.dispatch("share",r),Ne=({detail:r})=>g.dispatch("error",r),Pe=()=>g.dispatch("clear_status",p),qe=(...r)=>g.client.upload(...r),Ce=(...r)=>g.client.stream(...r);function Ue(r){T=r,n(22,T)}const Ee=({detail:r})=>n(26,Z=r),Je=()=>g.dispatch("clear"),Oe=()=>g.dispatch("play"),We=()=>g.dispatch("pause"),De=()=>g.dispatch("upload"),Le=()=>g.dispatch("stop"),Te=()=>g.dispatch("end"),Ae=()=>g.dispatch("start_recording"),Fe=()=>g.dispatch("stop_recording");return t.$$set=r=>{"elem_id"in r&&n(2,l=r.elem_id),"elem_classes"in r&&n(3,o=r.elem_classes),"visible"in r&&n(4,i=r.visible),"value"in r&&n(0,f=r.value),"label"in r&&n(5,s=r.label),"sources"in r&&n(6,h=r.sources),"root"in r&&n(7,a=r.root),"show_label"in r&&n(8,c=r.show_label),"loading_status"in r&&n(1,p=r.loading_status),"height"in r&&n(9,m=r.height),"width"in r&&n(10,P=r.width),"container"in r&&n(11,d=r.container),"scale"in r&&n(12,w=r.scale),"min_width"in r&&n(13,V=r.min_width),"autoplay"in r&&n(14,B=r.autoplay),"show_share_button"in r&&n(15,I=r.show_share_button),"show_download_button"in r&&n(16,A=r.show_download_button),"gradio"in r&&n(17,g=r.gradio),"interactive"in r&&n(18,E=r.interactive),"webcam_options"in r&&n(19,F=r.webcam_options),"include_audio"in r&&n(20,k=r.include_audio),"loop"in r&&n(21,Y=r.loop),"input_ready"in r&&n(30,G=r.input_ready)},t.$$.update=()=>{t.$$.dirty[0]&4194304&&n(30,G=!T),t.$$.dirty[0]&1|t.$$.dirty[1]&2&&f&&C===null&&n(32,C=f),t.$$.dirty[0]&8388672&&h&&!K&&n(23,K=h[0]),t.$$.dirty[0]&1&&(f!=null?(n(24,H=f.video),n(25,q=f.subtitles)):(n(24,H=null),n(25,q=null))),t.$$.dirty[0]&131073|t.$$.dirty[1]&1&&JSON.stringify(f)!==JSON.stringify(_)&&(n(31,_=f),g.dispatch("change"))},[f,p,l,o,i,s,h,a,c,m,P,d,w,V,B,I,A,g,E,F,k,Y,T,K,H,q,Z,$,x,ee,G,_,C,te,le,ne,oe,ie,se,u,Ne,Pe,qe,Ce,Ue,Ee,Je,Oe,We,De,Le,Te,Ae,Fe]}class Ut extends gt{constructor(e){super(),St(this,e,Ct,qt,Vt,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,sources:6,root:7,show_label:8,loading_status:1,height:9,width:10,container:11,scale:12,min_width:13,autoplay:14,show_share_button:15,show_download_button:16,gradio:17,interactive:18,webcam_options:19,include_audio:20,loop:21,input_ready:30},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),b()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),b()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),b()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),b()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),b()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),b()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),b()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),b()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),b()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),b()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),b()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),b()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),b()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),b()}get autoplay(){return this.$$.ctx[14]}set autoplay(e){this.$$set({autoplay:e}),b()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),b()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),b()}get gradio(){return this.$$.ctx[17]}set gradio(e){this.$$set({gradio:e}),b()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),b()}get webcam_options(){return this.$$.ctx[19]}set webcam_options(e){this.$$set({webcam_options:e}),b()}get include_audio(){return this.$$.ctx[20]}set include_audio(e){this.$$set({include_audio:e}),b()}get loop(){return this.$$.ctx[21]}set loop(e){this.$$set({loop:e}),b()}get input_ready(){return this.$$.ctx[30]}set input_ready(e){this.$$set({input_ready:e}),b()}}const Sl=Ut;export{Pl as BaseExample,mt as BaseInteractiveVideo,Ye as BasePlayer,Ze as BaseStaticVideo,Sl as default,Il as loaded,jl as playable,be as prettyBytes};
//# sourceMappingURL=index-BeWruw2y.js.map
