import{aR as p}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";function O(t){return t.split(" ").filter(s=>s!=="").map(s=>parseFloat(s))}function x(t,s,o){for(;o.length!==s;){const n=O(t.lines[t.index++]);o.push(...n)}}function V(t,s,o){let n=0,e=0,r=0,u=0,f=0,h=0;for(let i=0;i<t.numberOfHorizontalAngles-1;i++)if(o<t.horizontalAngles[i+1]||i===t.numberOfHorizontalAngles-2){e=i,r=t.horizontalAngles[i],u=t.horizontalAngles[i+1];break}for(let i=0;i<t.numberOfVerticalAngles-1;i++)if(s<t.verticalAngles[i+1]||i===t.numberOfVerticalAngles-2){n=i,f=t.verticalAngles[i],h=t.verticalAngles[i+1];break}const c=u-r,A=h-f;if(A===0)return 0;const g=c===0?0:(o-r)/c,m=(s-f)/A,d=c===0?e:e+1,l=p(t.candelaValues[e][n],t.candelaValues[d][n],g),a=p(t.candelaValues[e][n+1],t.candelaValues[d][n+1],g);return p(l,a,m)}function z(t){const n={lines:new TextDecoder("utf-8").decode(t).split(`
`),index:0},e={version:n.lines[0],candelaValues:[],horizontalAngles:[],verticalAngles:[],numberOfHorizontalAngles:0,numberOfVerticalAngles:0};for(n.index=1;n.lines.length>0&&!n.lines[n.index].includes("TILT=");)n.index++;n.lines[n.index].includes("INCLUDE"),n.index++;const r=O(n.lines[n.index++]);e.numberOfLights=r[0],e.lumensPerLamp=r[1],e.candelaMultiplier=r[2],e.numberOfVerticalAngles=r[3],e.numberOfHorizontalAngles=r[4],e.photometricType=r[5],e.unitsType=r[6],e.width=r[7],e.length=r[8],e.height=r[9];const u=O(n.lines[n.index++]);e.ballastFactor=u[0],e.fileGenerationType=u[1],e.inputWatts=u[2];for(let l=0;l<e.numberOfHorizontalAngles;l++)e.candelaValues[l]=[];x(n,e.numberOfVerticalAngles,e.verticalAngles),x(n,e.numberOfHorizontalAngles,e.horizontalAngles);for(let l=0;l<e.numberOfHorizontalAngles;l++)x(n,e.numberOfVerticalAngles,e.candelaValues[l]);let f=-1;for(let l=0;l<e.numberOfHorizontalAngles;l++)for(let a=0;a<e.numberOfVerticalAngles;a++)e.candelaValues[l][a]*=e.candelaValues[l][a]*e.candelaMultiplier*e.ballastFactor*e.fileGenerationType,f=Math.max(f,e.candelaValues[l][a]);if(f>0)for(let l=0;l<e.numberOfHorizontalAngles;l++)for(let a=0;a<e.numberOfVerticalAngles;a++)e.candelaValues[l][a]/=f;const h=180,c=h*2,A=c*h,g=new Float32Array(c*h),m=e.horizontalAngles[0],d=e.horizontalAngles[e.numberOfHorizontalAngles-1];for(let l=0;l<A;l++){let a=l%c;const b=Math.floor(l/c);d-m!==0&&(a<m||a>=d)&&(a%=d*2,a>d&&(a=d*2-a)),g[b+a*h]=V(e,b,a)}return{width:c/2,height:1,data:g}}class w{constructor(){this.supportCascades=!1}loadCubeData(){throw".ies not supported in Cube."}loadData(s,o,n){const e=new Uint8Array(s.buffer,s.byteOffset,s.byteLength),r=z(e);n(r.width,r.height,!1,!1,()=>{const u=o.getEngine();o.type=1,o.format=6,o._gammaSpace=!1,u._uploadDataToTextureDirectly(o,r.data)})}}export{w as _IESTextureLoader};
//# sourceMappingURL=iesTextureLoader-K9pNzTMi.js.map
