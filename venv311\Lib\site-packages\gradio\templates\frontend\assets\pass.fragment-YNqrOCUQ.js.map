{"version": 3, "file": "pass.fragment-YNqrOCUQ.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/pass.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"passPixelShader\";\nconst shader = `varying vUV: vec2f;var textureSamplerSampler: sampler;var textureSampler: texture_2d<f32>;\n#define CUSTOM_FRAGMENT_DEFINITIONS\n@fragment\nfn main(input: FragmentInputs)->FragmentOutputs {fragmentOutputs.color=textureSample(textureSampler,textureSamplerSampler,input.vUV);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const passPixelShaderWGSL = { name, shader };\n//# sourceMappingURL=pass.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "passPixelShaderWGSL"], "mappings": "+FAEA,MAAMA,EAAO,kBACPC,EAAS;AAAA;AAAA;AAAA,wIAKVC,EAAY,iBAAiBF,CAAI,IAClCE,EAAY,iBAAiBF,CAAI,EAAIC,GAG7B,MAACE,EAAsB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}