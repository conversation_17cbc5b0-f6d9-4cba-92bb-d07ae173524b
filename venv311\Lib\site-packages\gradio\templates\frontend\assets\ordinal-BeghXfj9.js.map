{"version": 3, "file": "ordinal-BeghXfj9.js", "sources": ["../../../../node_modules/.pnpm/internmap@2.0.3/node_modules/internmap/src/index.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/ordinal.js"], "sourcesContent": ["export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "import {InternMap} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport const implicit = Symbol(\"implicit\");\n\nexport default function ordinal() {\n  var index = new InternMap(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n"], "names": ["InternMap", "entries", "key", "keyof", "value", "intern_get", "intern_set", "intern_delete", "InternSet", "values", "_intern", "_key", "implicit", "ordinal", "index", "domain", "range", "unknown", "scale", "d", "_", "initRange"], "mappings": "uCAAO,MAAMA,UAAkB,GAAI,CACjC,YAAYC,EAASC,EAAMC,EAAO,CAGhC,GAFA,QACA,OAAO,iBAAiB,KAAM,CAAC,QAAS,CAAC,MAAO,IAAI,GAAK,EAAG,KAAM,CAAC,MAAOD,CAAG,CAAC,CAAC,EAC3ED,GAAW,KAAM,SAAW,CAACC,EAAKE,CAAK,IAAKH,EAAS,KAAK,IAAIC,EAAKE,CAAK,CAC7E,CACD,IAAIF,EAAK,CACP,OAAO,MAAM,IAAIG,EAAW,KAAMH,CAAG,CAAC,CACvC,CACD,IAAIA,EAAK,CACP,OAAO,MAAM,IAAIG,EAAW,KAAMH,CAAG,CAAC,CACvC,CACD,IAAIA,EAAKE,EAAO,CACd,OAAO,MAAM,IAAIE,EAAW,KAAMJ,CAAG,EAAGE,CAAK,CAC9C,CACD,OAAOF,EAAK,CACV,OAAO,MAAM,OAAOK,EAAc,KAAML,CAAG,CAAC,CAC7C,CACH,CAEO,MAAMM,UAAkB,GAAI,CACjC,YAAYC,EAAQP,EAAMC,EAAO,CAG/B,GAFA,QACA,OAAO,iBAAiB,KAAM,CAAC,QAAS,CAAC,MAAO,IAAI,GAAK,EAAG,KAAM,CAAC,MAAOD,CAAG,CAAC,CAAC,EAC3EO,GAAU,KAAM,UAAWL,KAASK,EAAQ,KAAK,IAAIL,CAAK,CAC/D,CACD,IAAIA,EAAO,CACT,OAAO,MAAM,IAAIC,EAAW,KAAMD,CAAK,CAAC,CACzC,CACD,IAAIA,EAAO,CACT,OAAO,MAAM,IAAIE,EAAW,KAAMF,CAAK,CAAC,CACzC,CACD,OAAOA,EAAO,CACZ,OAAO,MAAM,OAAOG,EAAc,KAAMH,CAAK,CAAC,CAC/C,CACH,CAEA,SAASC,EAAW,CAAC,QAAAK,EAAS,KAAAC,CAAI,EAAGP,EAAO,CAC1C,MAAMF,EAAMS,EAAKP,CAAK,EACtB,OAAOM,EAAQ,IAAIR,CAAG,EAAIQ,EAAQ,IAAIR,CAAG,EAAIE,CAC/C,CAEA,SAASE,EAAW,CAAC,QAAAI,EAAS,KAAAC,CAAI,EAAGP,EAAO,CAC1C,MAAMF,EAAMS,EAAKP,CAAK,EACtB,OAAIM,EAAQ,IAAIR,CAAG,EAAUQ,EAAQ,IAAIR,CAAG,GAC5CQ,EAAQ,IAAIR,EAAKE,CAAK,EACfA,EACT,CAEA,SAASG,EAAc,CAAC,QAAAG,EAAS,KAAAC,CAAI,EAAGP,EAAO,CAC7C,MAAMF,EAAMS,EAAKP,CAAK,EACtB,OAAIM,EAAQ,IAAIR,CAAG,IACjBE,EAAQM,EAAQ,IAAIR,CAAG,EACvBQ,EAAQ,OAAOR,CAAG,GAEbE,CACT,CAEA,SAASD,EAAMC,EAAO,CACpB,OAAOA,IAAU,MAAQ,OAAOA,GAAU,SAAWA,EAAM,QAAS,EAAGA,CACzE,CCzDY,MAACQ,EAAW,OAAO,UAAU,EAE1B,SAASC,GAAU,CAChC,IAAIC,EAAQ,IAAId,EACZe,EAAS,CAAE,EACXC,EAAQ,CAAE,EACVC,EAAUL,EAEd,SAASM,EAAMC,EAAG,CAChB,IAAI,EAAIL,EAAM,IAAIK,CAAC,EACnB,GAAI,IAAM,OAAW,CACnB,GAAIF,IAAYL,EAAU,OAAOK,EACjCH,EAAM,IAAIK,EAAG,EAAIJ,EAAO,KAAKI,CAAC,EAAI,CAAC,CACpC,CACD,OAAOH,EAAM,EAAIA,EAAM,MAAM,CAC9B,CAED,OAAAE,EAAM,OAAS,SAASE,EAAG,CACzB,GAAI,CAAC,UAAU,OAAQ,OAAOL,EAAO,MAAK,EAC1CA,EAAS,CAAE,EAAED,EAAQ,IAAId,EACzB,UAAWI,KAASgB,EACdN,EAAM,IAAIV,CAAK,GACnBU,EAAM,IAAIV,EAAOW,EAAO,KAAKX,CAAK,EAAI,CAAC,EAEzC,OAAOc,CACX,EAEEA,EAAM,MAAQ,SAASE,EAAG,CACxB,OAAO,UAAU,QAAUJ,EAAQ,MAAM,KAAKI,CAAC,EAAGF,GAASF,EAAM,OACrE,EAEEE,EAAM,QAAU,SAASE,EAAG,CAC1B,OAAO,UAAU,QAAUH,EAAUG,EAAGF,GAASD,CACrD,EAEEC,EAAM,KAAO,UAAW,CACtB,OAAOL,EAAQE,EAAQC,CAAK,EAAE,QAAQC,CAAO,CACjD,EAEEI,EAAU,MAAMH,EAAO,SAAS,EAEzBA,CACT", "x_google_ignoreList": [0, 1]}