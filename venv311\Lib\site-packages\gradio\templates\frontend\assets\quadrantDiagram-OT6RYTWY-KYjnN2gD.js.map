{"version": 3, "file": "quadrantDiagram-OT6RYTWY-KYjnN2gD.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-OT6RYTWY.mjs"], "sourcesContent": ["import {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/quadrant-chart/parser/quadrant.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 3], $V1 = [1, 4], $V2 = [1, 5], $V3 = [1, 6], $V4 = [1, 7], $V5 = [1, 4, 5, 10, 12, 13, 14, 18, 25, 35, 37, 39, 41, 42, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 63, 64, 65, 66, 67], $V6 = [1, 4, 5, 10, 12, 13, 14, 18, 25, 28, 35, 37, 39, 41, 42, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 63, 64, 65, 66, 67], $V7 = [55, 56, 57], $V8 = [2, 36], $V9 = [1, 37], $Va = [1, 36], $Vb = [1, 38], $Vc = [1, 35], $Vd = [1, 43], $Ve = [1, 41], $Vf = [1, 14], $Vg = [1, 23], $Vh = [1, 18], $Vi = [1, 19], $Vj = [1, 20], $Vk = [1, 21], $Vl = [1, 22], $Vm = [1, 24], $Vn = [1, 25], $Vo = [1, 26], $Vp = [1, 27], $Vq = [1, 28], $Vr = [1, 29], $Vs = [1, 32], $Vt = [1, 33], $Vu = [1, 34], $Vv = [1, 39], $Vw = [1, 40], $Vx = [1, 42], $Vy = [1, 44], $Vz = [1, 62], $VA = [1, 61], $VB = [4, 5, 8, 10, 12, 13, 14, 18, 44, 47, 49, 55, 56, 57, 63, 64, 65, 66, 67], $VC = [1, 65], $VD = [1, 66], $VE = [1, 67], $VF = [1, 68], $VG = [1, 69], $VH = [1, 70], $VI = [1, 71], $VJ = [1, 72], $VK = [1, 73], $VL = [1, 74], $VM = [1, 75], $VN = [1, 76], $VO = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18], $VP = [1, 90], $VQ = [1, 91], $VR = [1, 92], $VS = [1, 99], $VT = [1, 93], $VU = [1, 96], $VV = [1, 94], $VW = [1, 95], $VX = [1, 97], $VY = [1, 98], $VZ = [1, 102], $V_ = [10, 55, 56, 57], $V$ = [4, 5, 6, 8, 10, 11, 13, 17, 18, 19, 20, 55, 56, 57];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"idStringToken\": 3, \"ALPHA\": 4, \"NUM\": 5, \"NODE_STRING\": 6, \"DOWN\": 7, \"MINUS\": 8, \"DEFAULT\": 9, \"COMMA\": 10, \"COLON\": 11, \"AMP\": 12, \"BRKT\": 13, \"MULT\": 14, \"UNICODE_TEXT\": 15, \"styleComponent\": 16, \"UNIT\": 17, \"SPACE\": 18, \"STYLE\": 19, \"PCT\": 20, \"idString\": 21, \"style\": 22, \"stylesOpt\": 23, \"classDefStatement\": 24, \"CLASSDEF\": 25, \"start\": 26, \"eol\": 27, \"QUADRANT\": 28, \"document\": 29, \"line\": 30, \"statement\": 31, \"axisDetails\": 32, \"quadrantDetails\": 33, \"points\": 34, \"title\": 35, \"title_value\": 36, \"acc_title\": 37, \"acc_title_value\": 38, \"acc_descr\": 39, \"acc_descr_value\": 40, \"acc_descr_multiline_value\": 41, \"section\": 42, \"text\": 43, \"point_start\": 44, \"point_x\": 45, \"point_y\": 46, \"class_name\": 47, \"X-AXIS\": 48, \"AXIS-TEXT-DELIMITER\": 49, \"Y-AXIS\": 50, \"QUADRANT_1\": 51, \"QUADRANT_2\": 52, \"QUADRANT_3\": 53, \"QUADRANT_4\": 54, \"NEWLINE\": 55, \"SEMI\": 56, \"EOF\": 57, \"alphaNumToken\": 58, \"textNoTagsToken\": 59, \"STR\": 60, \"MD_STR\": 61, \"alphaNum\": 62, \"PUNCTUATION\": 63, \"PLUS\": 64, \"EQUALS\": 65, \"DOT\": 66, \"UNDERSCORE\": 67, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"ALPHA\", 5: \"NUM\", 6: \"NODE_STRING\", 7: \"DOWN\", 8: \"MINUS\", 9: \"DEFAULT\", 10: \"COMMA\", 11: \"COLON\", 12: \"AMP\", 13: \"BRKT\", 14: \"MULT\", 15: \"UNICODE_TEXT\", 17: \"UNIT\", 18: \"SPACE\", 19: \"STYLE\", 20: \"PCT\", 25: \"CLASSDEF\", 28: \"QUADRANT\", 35: \"title\", 36: \"title_value\", 37: \"acc_title\", 38: \"acc_title_value\", 39: \"acc_descr\", 40: \"acc_descr_value\", 41: \"acc_descr_multiline_value\", 42: \"section\", 44: \"point_start\", 45: \"point_x\", 46: \"point_y\", 47: \"class_name\", 48: \"X-AXIS\", 49: \"AXIS-TEXT-DELIMITER\", 50: \"Y-AXIS\", 51: \"QUADRANT_1\", 52: \"QUADRANT_2\", 53: \"QUADRANT_3\", 54: \"QUADRANT_4\", 55: \"NEWLINE\", 56: \"SEMI\", 57: \"EOF\", 60: \"STR\", 61: \"MD_STR\", 63: \"PUNCTUATION\", 64: \"PLUS\", 65: \"EQUALS\", 66: \"DOT\", 67: \"UNDERSCORE\" },\n    productions_: [0, [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [21, 1], [21, 2], [22, 1], [22, 2], [23, 1], [23, 3], [24, 5], [26, 2], [26, 2], [26, 2], [29, 0], [29, 2], [30, 2], [31, 0], [31, 1], [31, 2], [31, 1], [31, 1], [31, 1], [31, 2], [31, 2], [31, 2], [31, 1], [31, 1], [34, 4], [34, 5], [34, 5], [34, 6], [32, 4], [32, 3], [32, 2], [32, 4], [32, 3], [32, 2], [33, 2], [33, 2], [33, 2], [33, 2], [27, 1], [27, 1], [27, 1], [43, 1], [43, 2], [43, 1], [43, 1], [62, 1], [62, 2], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [59, 1], [59, 1], [59, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 23:\n          this.$ = $$[$0];\n          break;\n        case 24:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n        case 26:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 27:\n          this.$ = [$$[$0].trim()];\n          break;\n        case 28:\n          $$[$0 - 2].push($$[$0].trim());\n          this.$ = $$[$0 - 2];\n          break;\n        case 29:\n          this.$ = $$[$0 - 4];\n          yy.addClass($$[$0 - 2], $$[$0]);\n          break;\n        case 37:\n          this.$ = [];\n          break;\n        case 42:\n          this.$ = $$[$0].trim();\n          yy.setDiagramTitle(this.$);\n          break;\n        case 43:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 44:\n        case 45:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 46:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 47:\n          yy.addPoint($$[$0 - 3], \"\", $$[$0 - 1], $$[$0], []);\n          break;\n        case 48:\n          yy.addPoint($$[$0 - 4], $$[$0 - 3], $$[$0 - 1], $$[$0], []);\n          break;\n        case 49:\n          yy.addPoint($$[$0 - 4], \"\", $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 50:\n          yy.addPoint($$[$0 - 5], $$[$0 - 4], $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 51:\n          yy.setXAxisLeftText($$[$0 - 2]);\n          yy.setXAxisRightText($$[$0]);\n          break;\n        case 52:\n          $$[$0 - 1].text += \" \\u27F6 \";\n          yy.setXAxisLeftText($$[$0 - 1]);\n          break;\n        case 53:\n          yy.setXAxisLeftText($$[$0]);\n          break;\n        case 54:\n          yy.setYAxisBottomText($$[$0 - 2]);\n          yy.setYAxisTopText($$[$0]);\n          break;\n        case 55:\n          $$[$0 - 1].text += \" \\u27F6 \";\n          yy.setYAxisBottomText($$[$0 - 1]);\n          break;\n        case 56:\n          yy.setYAxisBottomText($$[$0]);\n          break;\n        case 57:\n          yy.setQuadrant1Text($$[$0]);\n          break;\n        case 58:\n          yy.setQuadrant2Text($$[$0]);\n          break;\n        case 59:\n          yy.setQuadrant3Text($$[$0]);\n          break;\n        case 60:\n          yy.setQuadrant4Text($$[$0]);\n          break;\n        case 64:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 65:\n          this.$ = { text: $$[$0 - 1].text + \"\" + $$[$0], type: $$[$0 - 1].type };\n          break;\n        case 66:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 67:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 68:\n          this.$ = $$[$0];\n          break;\n        case 69:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 18: $V0, 26: 1, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, { 1: [3] }, { 18: $V0, 26: 8, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, { 18: $V0, 26: 9, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, o($V5, [2, 33], { 29: 10 }), o($V6, [2, 61]), o($V6, [2, 62]), o($V6, [2, 63]), { 1: [2, 30] }, { 1: [2, 31] }, o($V7, $V8, { 30: 11, 31: 12, 24: 13, 32: 15, 33: 16, 34: 17, 43: 30, 58: 31, 1: [2, 32], 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $Vf, 25: $Vg, 35: $Vh, 37: $Vi, 39: $Vj, 41: $Vk, 42: $Vl, 48: $Vm, 50: $Vn, 51: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V5, [2, 34]), { 27: 45, 55: $V2, 56: $V3, 57: $V4 }, o($V7, [2, 37]), o($V7, $V8, { 24: 13, 32: 15, 33: 16, 34: 17, 43: 30, 58: 31, 31: 46, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $Vf, 25: $Vg, 35: $Vh, 37: $Vi, 39: $Vj, 41: $Vk, 42: $Vl, 48: $Vm, 50: $Vn, 51: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 39]), o($V7, [2, 40]), o($V7, [2, 41]), { 36: [1, 47] }, { 38: [1, 48] }, { 40: [1, 49] }, o($V7, [2, 45]), o($V7, [2, 46]), { 18: [1, 50] }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 51, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 52, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 53, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 54, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 55, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 56, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 44: [1, 57], 47: [1, 58], 58: 60, 59: 59, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, o($VB, [2, 64]), o($VB, [2, 66]), o($VB, [2, 67]), o($VB, [2, 70]), o($VB, [2, 71]), o($VB, [2, 72]), o($VB, [2, 73]), o($VB, [2, 74]), o($VB, [2, 75]), o($VB, [2, 76]), o($VB, [2, 77]), o($VB, [2, 78]), o($VB, [2, 79]), o($VB, [2, 80]), o($V5, [2, 35]), o($V7, [2, 38]), o($V7, [2, 42]), o($V7, [2, 43]), o($V7, [2, 44]), { 3: 64, 4: $VC, 5: $VD, 6: $VE, 7: $VF, 8: $VG, 9: $VH, 10: $VI, 11: $VJ, 12: $VK, 13: $VL, 14: $VM, 15: $VN, 21: 63 }, o($V7, [2, 53], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 49: [1, 77], 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 56], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 49: [1, 78], 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 57], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 58], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 59], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 60], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), { 45: [1, 79] }, { 44: [1, 80] }, o($VB, [2, 65]), o($VB, [2, 81]), o($VB, [2, 82]), o($VB, [2, 83]), { 3: 82, 4: $VC, 5: $VD, 6: $VE, 7: $VF, 8: $VG, 9: $VH, 10: $VI, 11: $VJ, 12: $VK, 13: $VL, 14: $VM, 15: $VN, 18: [1, 81] }, o($VO, [2, 23]), o($VO, [2, 1]), o($VO, [2, 2]), o($VO, [2, 3]), o($VO, [2, 4]), o($VO, [2, 5]), o($VO, [2, 6]), o($VO, [2, 7]), o($VO, [2, 8]), o($VO, [2, 9]), o($VO, [2, 10]), o($VO, [2, 11]), o($VO, [2, 12]), o($V7, [2, 52], { 58: 31, 43: 83, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 55], { 58: 31, 43: 84, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), { 46: [1, 85] }, { 45: [1, 86] }, { 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 16: 89, 17: $VV, 18: $VW, 19: $VX, 20: $VY, 22: 88, 23: 87 }, o($VO, [2, 24]), o($V7, [2, 51], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 54], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 47], { 22: 88, 16: 89, 23: 100, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), { 46: [1, 101] }, o($V7, [2, 29], { 10: $VZ }), o($V_, [2, 27], { 16: 103, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), o($V$, [2, 25]), o($V$, [2, 13]), o($V$, [2, 14]), o($V$, [2, 15]), o($V$, [2, 16]), o($V$, [2, 17]), o($V$, [2, 18]), o($V$, [2, 19]), o($V$, [2, 20]), o($V$, [2, 21]), o($V$, [2, 22]), o($V7, [2, 49], { 10: $VZ }), o($V7, [2, 48], { 22: 88, 16: 89, 23: 104, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), { 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 16: 89, 17: $VV, 18: $VW, 19: $VX, 20: $VY, 22: 105 }, o($V$, [2, 26]), o($V7, [2, 50], { 10: $VZ }), o($V_, [2, 28], { 16: 103, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY })],\n    defaultActions: { 8: [2, 30], 9: [2, 31] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 55;\n            break;\n          case 3:\n            break;\n          case 4:\n            this.begin(\"title\");\n            return 35;\n            break;\n          case 5:\n            this.popState();\n            return \"title_value\";\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 37;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 39;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            return 48;\n            break;\n          case 14:\n            return 50;\n            break;\n          case 15:\n            return 49;\n            break;\n          case 16:\n            return 51;\n            break;\n          case 17:\n            return 52;\n            break;\n          case 18:\n            return 53;\n            break;\n          case 19:\n            return 54;\n            break;\n          case 20:\n            return 25;\n            break;\n          case 21:\n            this.begin(\"md_string\");\n            break;\n          case 22:\n            return \"MD_STR\";\n            break;\n          case 23:\n            this.popState();\n            break;\n          case 24:\n            this.begin(\"string\");\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            return \"STR\";\n            break;\n          case 27:\n            this.begin(\"class_name\");\n            break;\n          case 28:\n            this.popState();\n            return 47;\n            break;\n          case 29:\n            this.begin(\"point_start\");\n            return 44;\n            break;\n          case 30:\n            this.begin(\"point_x\");\n            return 45;\n            break;\n          case 31:\n            this.popState();\n            break;\n          case 32:\n            this.popState();\n            this.begin(\"point_y\");\n            break;\n          case 33:\n            this.popState();\n            return 46;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 4;\n            break;\n          case 36:\n            return 11;\n            break;\n          case 37:\n            return 64;\n            break;\n          case 38:\n            return 10;\n            break;\n          case 39:\n            return 65;\n            break;\n          case 40:\n            return 65;\n            break;\n          case 41:\n            return 14;\n            break;\n          case 42:\n            return 13;\n            break;\n          case 43:\n            return 67;\n            break;\n          case 44:\n            return 66;\n            break;\n          case 45:\n            return 12;\n            break;\n          case 46:\n            return 8;\n            break;\n          case 47:\n            return 5;\n            break;\n          case 48:\n            return 18;\n            break;\n          case 49:\n            return 56;\n            break;\n          case 50:\n            return 63;\n            break;\n          case 51:\n            return 57;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?: *x-axis *)/i, /^(?: *y-axis *)/i, /^(?: *--+> *)/i, /^(?: *quadrant-1 *)/i, /^(?: *quadrant-2 *)/i, /^(?: *quadrant-3 *)/i, /^(?: *quadrant-4 *)/i, /^(?:classDef\\b)/i, /^(?:[\"][`])/i, /^(?:[^`\"]+)/i, /^(?:[`][\"])/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?::::)/i, /^(?:^\\w+)/i, /^(?:\\s*:\\s*\\[\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?:\\s*\\] *)/i, /^(?:\\s*,\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?: *quadrantChart *)/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s)/i, /^(?:;)/i, /^(?:[!\"#$%&'*+,-.`?\\\\_/])/i, /^(?:$)/i],\n      conditions: { \"class_name\": { \"rules\": [28], \"inclusive\": false }, \"point_y\": { \"rules\": [33], \"inclusive\": false }, \"point_x\": { \"rules\": [32], \"inclusive\": false }, \"point_start\": { \"rules\": [30, 31], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"title\": { \"rules\": [5], \"inclusive\": false }, \"md_string\": { \"rules\": [22, 23], \"inclusive\": false }, \"string\": { \"rules\": [25, 26], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 24, 27, 29, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar quadrant_default = parser;\n\n// src/diagrams/quadrant-chart/quadrantBuilder.ts\nimport { scaleLinear } from \"d3\";\nvar defaultThemeVariables = getThemeVariables();\nvar QuadrantBuilder = class {\n  constructor() {\n    this.classes = /* @__PURE__ */ new Map();\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n  }\n  static {\n    __name(this, \"QuadrantBuilder\");\n  }\n  getDefaultData() {\n    return {\n      titleText: \"\",\n      quadrant1Text: \"\",\n      quadrant2Text: \"\",\n      quadrant3Text: \"\",\n      quadrant4Text: \"\",\n      xAxisLeftText: \"\",\n      xAxisRightText: \"\",\n      yAxisBottomText: \"\",\n      yAxisTopText: \"\",\n      points: []\n    };\n  }\n  getDefaultConfig() {\n    return {\n      showXAxis: true,\n      showYAxis: true,\n      showTitle: true,\n      chartHeight: defaultConfig_default.quadrantChart?.chartWidth || 500,\n      chartWidth: defaultConfig_default.quadrantChart?.chartHeight || 500,\n      titlePadding: defaultConfig_default.quadrantChart?.titlePadding || 10,\n      titleFontSize: defaultConfig_default.quadrantChart?.titleFontSize || 20,\n      quadrantPadding: defaultConfig_default.quadrantChart?.quadrantPadding || 5,\n      xAxisLabelPadding: defaultConfig_default.quadrantChart?.xAxisLabelPadding || 5,\n      yAxisLabelPadding: defaultConfig_default.quadrantChart?.yAxisLabelPadding || 5,\n      xAxisLabelFontSize: defaultConfig_default.quadrantChart?.xAxisLabelFontSize || 16,\n      yAxisLabelFontSize: defaultConfig_default.quadrantChart?.yAxisLabelFontSize || 16,\n      quadrantLabelFontSize: defaultConfig_default.quadrantChart?.quadrantLabelFontSize || 16,\n      quadrantTextTopPadding: defaultConfig_default.quadrantChart?.quadrantTextTopPadding || 5,\n      pointTextPadding: defaultConfig_default.quadrantChart?.pointTextPadding || 5,\n      pointLabelFontSize: defaultConfig_default.quadrantChart?.pointLabelFontSize || 12,\n      pointRadius: defaultConfig_default.quadrantChart?.pointRadius || 5,\n      xAxisPosition: defaultConfig_default.quadrantChart?.xAxisPosition || \"top\",\n      yAxisPosition: defaultConfig_default.quadrantChart?.yAxisPosition || \"left\",\n      quadrantInternalBorderStrokeWidth: defaultConfig_default.quadrantChart?.quadrantInternalBorderStrokeWidth || 1,\n      quadrantExternalBorderStrokeWidth: defaultConfig_default.quadrantChart?.quadrantExternalBorderStrokeWidth || 2\n    };\n  }\n  getDefaultThemeConfig() {\n    return {\n      quadrant1Fill: defaultThemeVariables.quadrant1Fill,\n      quadrant2Fill: defaultThemeVariables.quadrant2Fill,\n      quadrant3Fill: defaultThemeVariables.quadrant3Fill,\n      quadrant4Fill: defaultThemeVariables.quadrant4Fill,\n      quadrant1TextFill: defaultThemeVariables.quadrant1TextFill,\n      quadrant2TextFill: defaultThemeVariables.quadrant2TextFill,\n      quadrant3TextFill: defaultThemeVariables.quadrant3TextFill,\n      quadrant4TextFill: defaultThemeVariables.quadrant4TextFill,\n      quadrantPointFill: defaultThemeVariables.quadrantPointFill,\n      quadrantPointTextFill: defaultThemeVariables.quadrantPointTextFill,\n      quadrantXAxisTextFill: defaultThemeVariables.quadrantXAxisTextFill,\n      quadrantYAxisTextFill: defaultThemeVariables.quadrantYAxisTextFill,\n      quadrantTitleFill: defaultThemeVariables.quadrantTitleFill,\n      quadrantInternalBorderStrokeFill: defaultThemeVariables.quadrantInternalBorderStrokeFill,\n      quadrantExternalBorderStrokeFill: defaultThemeVariables.quadrantExternalBorderStrokeFill\n    };\n  }\n  clear() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n    this.classes = /* @__PURE__ */ new Map();\n    log.info(\"clear called\");\n  }\n  setData(data) {\n    this.data = { ...this.data, ...data };\n  }\n  addPoints(points) {\n    this.data.points = [...points, ...this.data.points];\n  }\n  addClass(className, styles) {\n    this.classes.set(className, styles);\n  }\n  setConfig(config2) {\n    log.trace(\"setConfig called with: \", config2);\n    this.config = { ...this.config, ...config2 };\n  }\n  setThemeConfig(themeConfig) {\n    log.trace(\"setThemeConfig called with: \", themeConfig);\n    this.themeConfig = { ...this.themeConfig, ...themeConfig };\n  }\n  calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle) {\n    const xAxisSpaceCalculation = this.config.xAxisLabelPadding * 2 + this.config.xAxisLabelFontSize;\n    const xAxisSpace = {\n      top: xAxisPosition === \"top\" && showXAxis ? xAxisSpaceCalculation : 0,\n      bottom: xAxisPosition === \"bottom\" && showXAxis ? xAxisSpaceCalculation : 0\n    };\n    const yAxisSpaceCalculation = this.config.yAxisLabelPadding * 2 + this.config.yAxisLabelFontSize;\n    const yAxisSpace = {\n      left: this.config.yAxisPosition === \"left\" && showYAxis ? yAxisSpaceCalculation : 0,\n      right: this.config.yAxisPosition === \"right\" && showYAxis ? yAxisSpaceCalculation : 0\n    };\n    const titleSpaceCalculation = this.config.titleFontSize + this.config.titlePadding * 2;\n    const titleSpace = {\n      top: showTitle ? titleSpaceCalculation : 0\n    };\n    const quadrantLeft = this.config.quadrantPadding + yAxisSpace.left;\n    const quadrantTop = this.config.quadrantPadding + xAxisSpace.top + titleSpace.top;\n    const quadrantWidth = this.config.chartWidth - this.config.quadrantPadding * 2 - yAxisSpace.left - yAxisSpace.right;\n    const quadrantHeight = this.config.chartHeight - this.config.quadrantPadding * 2 - xAxisSpace.top - xAxisSpace.bottom - titleSpace.top;\n    const quadrantHalfWidth = quadrantWidth / 2;\n    const quadrantHalfHeight = quadrantHeight / 2;\n    const quadrantSpace = {\n      quadrantLeft,\n      quadrantTop,\n      quadrantWidth,\n      quadrantHalfWidth,\n      quadrantHeight,\n      quadrantHalfHeight\n    };\n    return {\n      xAxisSpace,\n      yAxisSpace,\n      titleSpace,\n      quadrantSpace\n    };\n  }\n  getAxisLabels(xAxisPosition, showXAxis, showYAxis, spaceData) {\n    const { quadrantSpace, titleSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const drawXAxisLabelsInMiddle = Boolean(this.data.xAxisRightText);\n    const drawYAxisLabelsInMiddle = Boolean(this.data.yAxisTopText);\n    const axisLabels = [];\n    if (this.data.xAxisLeftText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisLeftText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.xAxisRightText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisRightText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + quadrantHalfWidth + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.yAxisBottomText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisBottomText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    if (this.data.yAxisTopText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisTopText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHalfHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    return axisLabels;\n  }\n  getQuadrants(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHalfHeight, quadrantLeft, quadrantHalfWidth, quadrantTop } = quadrantSpace;\n    const quadrants = [\n      {\n        text: {\n          text: this.data.quadrant1Text,\n          fill: this.themeConfig.quadrant1TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant1Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant2Text,\n          fill: this.themeConfig.quadrant2TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant2Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant3Text,\n          fill: this.themeConfig.quadrant3TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant3Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant4Text,\n          fill: this.themeConfig.quadrant4TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant4Fill\n      }\n    ];\n    for (const quadrant of quadrants) {\n      quadrant.text.x = quadrant.x + quadrant.width / 2;\n      if (this.data.points.length === 0) {\n        quadrant.text.y = quadrant.y + quadrant.height / 2;\n        quadrant.text.horizontalPos = \"middle\";\n      } else {\n        quadrant.text.y = quadrant.y + this.config.quadrantTextTopPadding;\n        quadrant.text.horizontalPos = \"top\";\n      }\n    }\n    return quadrants;\n  }\n  getQuadrantPoints(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHeight, quadrantLeft, quadrantTop, quadrantWidth } = quadrantSpace;\n    const xAxis = scaleLinear().domain([0, 1]).range([quadrantLeft, quadrantWidth + quadrantLeft]);\n    const yAxis = scaleLinear().domain([0, 1]).range([quadrantHeight + quadrantTop, quadrantTop]);\n    const points = this.data.points.map((point) => {\n      const classStyles = this.classes.get(point.className);\n      if (classStyles) {\n        point = { ...classStyles, ...point };\n      }\n      const props = {\n        x: xAxis(point.x),\n        y: yAxis(point.y),\n        fill: point.color ?? this.themeConfig.quadrantPointFill,\n        radius: point.radius ?? this.config.pointRadius,\n        text: {\n          text: point.text,\n          fill: this.themeConfig.quadrantPointTextFill,\n          x: xAxis(point.x),\n          y: yAxis(point.y) + this.config.pointTextPadding,\n          verticalPos: \"center\",\n          horizontalPos: \"top\",\n          fontSize: this.config.pointLabelFontSize,\n          rotation: 0\n        },\n        strokeColor: point.strokeColor ?? this.themeConfig.quadrantPointFill,\n        strokeWidth: point.strokeWidth ?? \"0px\"\n      };\n      return props;\n    });\n    return points;\n  }\n  getBorders(spaceData) {\n    const halfExternalBorderWidth = this.config.quadrantExternalBorderStrokeWidth / 2;\n    const { quadrantSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const borderLines = [\n      // top border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop\n      },\n      // right border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // bottom border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHeight,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHeight\n      },\n      // left border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // vertical inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantHalfWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantHalfWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // horizontal inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHalfHeight,\n        x2: quadrantLeft + quadrantWidth - halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHalfHeight\n      }\n    ];\n    return borderLines;\n  }\n  getTitle(showTitle) {\n    if (showTitle) {\n      return {\n        text: this.data.titleText,\n        fill: this.themeConfig.quadrantTitleFill,\n        fontSize: this.config.titleFontSize,\n        horizontalPos: \"top\",\n        verticalPos: \"center\",\n        rotation: 0,\n        y: this.config.titlePadding,\n        x: this.config.chartWidth / 2\n      };\n    }\n    return;\n  }\n  build() {\n    const showXAxis = this.config.showXAxis && !!(this.data.xAxisLeftText || this.data.xAxisRightText);\n    const showYAxis = this.config.showYAxis && !!(this.data.yAxisTopText || this.data.yAxisBottomText);\n    const showTitle = this.config.showTitle && !!this.data.titleText;\n    const xAxisPosition = this.data.points.length > 0 ? \"bottom\" : this.config.xAxisPosition;\n    const calculatedSpace = this.calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle);\n    return {\n      points: this.getQuadrantPoints(calculatedSpace),\n      quadrants: this.getQuadrants(calculatedSpace),\n      axisLabels: this.getAxisLabels(xAxisPosition, showXAxis, showYAxis, calculatedSpace),\n      borderLines: this.getBorders(calculatedSpace),\n      title: this.getTitle(showTitle)\n    };\n  }\n};\n\n// src/diagrams/quadrant-chart/utils.ts\nvar InvalidStyleError = class extends Error {\n  static {\n    __name(this, \"InvalidStyleError\");\n  }\n  constructor(style, value, type) {\n    super(`value for ${style} ${value} is invalid, please use a valid ${type}`);\n    this.name = \"InvalidStyleError\";\n  }\n};\nfunction validateHexCode(value) {\n  return !/^#?([\\dA-Fa-f]{6}|[\\dA-Fa-f]{3})$/.test(value);\n}\n__name(validateHexCode, \"validateHexCode\");\nfunction validateNumber(value) {\n  return !/^\\d+$/.test(value);\n}\n__name(validateNumber, \"validateNumber\");\nfunction validateSizeInPixels(value) {\n  return !/^\\d+px$/.test(value);\n}\n__name(validateSizeInPixels, \"validateSizeInPixels\");\n\n// src/diagrams/quadrant-chart/quadrantDb.ts\nvar config = getConfig();\nfunction textSanitizer(text) {\n  return sanitizeText(text.trim(), config);\n}\n__name(textSanitizer, \"textSanitizer\");\nvar quadrantBuilder = new QuadrantBuilder();\nfunction setQuadrant1Text(textObj) {\n  quadrantBuilder.setData({ quadrant1Text: textSanitizer(textObj.text) });\n}\n__name(setQuadrant1Text, \"setQuadrant1Text\");\nfunction setQuadrant2Text(textObj) {\n  quadrantBuilder.setData({ quadrant2Text: textSanitizer(textObj.text) });\n}\n__name(setQuadrant2Text, \"setQuadrant2Text\");\nfunction setQuadrant3Text(textObj) {\n  quadrantBuilder.setData({ quadrant3Text: textSanitizer(textObj.text) });\n}\n__name(setQuadrant3Text, \"setQuadrant3Text\");\nfunction setQuadrant4Text(textObj) {\n  quadrantBuilder.setData({ quadrant4Text: textSanitizer(textObj.text) });\n}\n__name(setQuadrant4Text, \"setQuadrant4Text\");\nfunction setXAxisLeftText(textObj) {\n  quadrantBuilder.setData({ xAxisLeftText: textSanitizer(textObj.text) });\n}\n__name(setXAxisLeftText, \"setXAxisLeftText\");\nfunction setXAxisRightText(textObj) {\n  quadrantBuilder.setData({ xAxisRightText: textSanitizer(textObj.text) });\n}\n__name(setXAxisRightText, \"setXAxisRightText\");\nfunction setYAxisTopText(textObj) {\n  quadrantBuilder.setData({ yAxisTopText: textSanitizer(textObj.text) });\n}\n__name(setYAxisTopText, \"setYAxisTopText\");\nfunction setYAxisBottomText(textObj) {\n  quadrantBuilder.setData({ yAxisBottomText: textSanitizer(textObj.text) });\n}\n__name(setYAxisBottomText, \"setYAxisBottomText\");\nfunction parseStyles(styles) {\n  const stylesObject = {};\n  for (const style of styles) {\n    const [key, value] = style.trim().split(/\\s*:\\s*/);\n    if (key === \"radius\") {\n      if (validateNumber(value)) {\n        throw new InvalidStyleError(key, value, \"number\");\n      }\n      stylesObject.radius = parseInt(value);\n    } else if (key === \"color\") {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, \"hex code\");\n      }\n      stylesObject.color = value;\n    } else if (key === \"stroke-color\") {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, \"hex code\");\n      }\n      stylesObject.strokeColor = value;\n    } else if (key === \"stroke-width\") {\n      if (validateSizeInPixels(value)) {\n        throw new InvalidStyleError(key, value, \"number of pixels (eg. 10px)\");\n      }\n      stylesObject.strokeWidth = value;\n    } else {\n      throw new Error(`style named ${key} is not supported.`);\n    }\n  }\n  return stylesObject;\n}\n__name(parseStyles, \"parseStyles\");\nfunction addPoint(textObj, className, x, y, styles) {\n  const stylesObject = parseStyles(styles);\n  quadrantBuilder.addPoints([\n    {\n      x,\n      y,\n      text: textSanitizer(textObj.text),\n      className,\n      ...stylesObject\n    }\n  ]);\n}\n__name(addPoint, \"addPoint\");\nfunction addClass(className, styles) {\n  quadrantBuilder.addClass(className, parseStyles(styles));\n}\n__name(addClass, \"addClass\");\nfunction setWidth(width) {\n  quadrantBuilder.setConfig({ chartWidth: width });\n}\n__name(setWidth, \"setWidth\");\nfunction setHeight(height) {\n  quadrantBuilder.setConfig({ chartHeight: height });\n}\n__name(setHeight, \"setHeight\");\nfunction getQuadrantData() {\n  const config2 = getConfig();\n  const { themeVariables, quadrantChart: quadrantChartConfig } = config2;\n  if (quadrantChartConfig) {\n    quadrantBuilder.setConfig(quadrantChartConfig);\n  }\n  quadrantBuilder.setThemeConfig({\n    quadrant1Fill: themeVariables.quadrant1Fill,\n    quadrant2Fill: themeVariables.quadrant2Fill,\n    quadrant3Fill: themeVariables.quadrant3Fill,\n    quadrant4Fill: themeVariables.quadrant4Fill,\n    quadrant1TextFill: themeVariables.quadrant1TextFill,\n    quadrant2TextFill: themeVariables.quadrant2TextFill,\n    quadrant3TextFill: themeVariables.quadrant3TextFill,\n    quadrant4TextFill: themeVariables.quadrant4TextFill,\n    quadrantPointFill: themeVariables.quadrantPointFill,\n    quadrantPointTextFill: themeVariables.quadrantPointTextFill,\n    quadrantXAxisTextFill: themeVariables.quadrantXAxisTextFill,\n    quadrantYAxisTextFill: themeVariables.quadrantYAxisTextFill,\n    quadrantExternalBorderStrokeFill: themeVariables.quadrantExternalBorderStrokeFill,\n    quadrantInternalBorderStrokeFill: themeVariables.quadrantInternalBorderStrokeFill,\n    quadrantTitleFill: themeVariables.quadrantTitleFill\n  });\n  quadrantBuilder.setData({ titleText: getDiagramTitle() });\n  return quadrantBuilder.build();\n}\n__name(getQuadrantData, \"getQuadrantData\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  quadrantBuilder.clear();\n  clear();\n}, \"clear\");\nvar quadrantDb_default = {\n  setWidth,\n  setHeight,\n  setQuadrant1Text,\n  setQuadrant2Text,\n  setQuadrant3Text,\n  setQuadrant4Text,\n  setXAxisLeftText,\n  setXAxisRightText,\n  setYAxisTopText,\n  setYAxisBottomText,\n  parseStyles,\n  addPoint,\n  addClass,\n  getQuadrantData,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/quadrant-chart/quadrantRenderer.ts\nimport { select } from \"d3\";\nvar draw = /* @__PURE__ */ __name((txt, id, _version, diagObj) => {\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"hanging\" : \"middle\";\n  }\n  __name(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : \"middle\";\n  }\n  __name(getTextAnchor, \"getTextAnchor\");\n  function getTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  __name(getTransformation, \"getTransformation\");\n  const conf = getConfig();\n  log.debug(\"Rendering quadrant chart\\n\" + txt);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const width = conf.quadrantChart?.chartWidth ?? 500;\n  const height = conf.quadrantChart?.chartHeight ?? 500;\n  configureSvgSize(svg, height, width, conf.quadrantChart?.useMaxWidth ?? true);\n  svg.attr(\"viewBox\", \"0 0 \" + width + \" \" + height);\n  diagObj.db.setHeight(height);\n  diagObj.db.setWidth(width);\n  const quadrantData = diagObj.db.getQuadrantData();\n  const quadrantsGroup = group.append(\"g\").attr(\"class\", \"quadrants\");\n  const borderGroup = group.append(\"g\").attr(\"class\", \"border\");\n  const dataPointGroup = group.append(\"g\").attr(\"class\", \"data-points\");\n  const labelGroup = group.append(\"g\").attr(\"class\", \"labels\");\n  const titleGroup = group.append(\"g\").attr(\"class\", \"title\");\n  if (quadrantData.title) {\n    titleGroup.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", quadrantData.title.fill).attr(\"font-size\", quadrantData.title.fontSize).attr(\"dominant-baseline\", getDominantBaseLine(quadrantData.title.horizontalPos)).attr(\"text-anchor\", getTextAnchor(quadrantData.title.verticalPos)).attr(\"transform\", getTransformation(quadrantData.title)).text(quadrantData.title.text);\n  }\n  if (quadrantData.borderLines) {\n    borderGroup.selectAll(\"line\").data(quadrantData.borderLines).enter().append(\"line\").attr(\"x1\", (data) => data.x1).attr(\"y1\", (data) => data.y1).attr(\"x2\", (data) => data.x2).attr(\"y2\", (data) => data.y2).style(\"stroke\", (data) => data.strokeFill).style(\"stroke-width\", (data) => data.strokeWidth);\n  }\n  const quadrants = quadrantsGroup.selectAll(\"g.quadrant\").data(quadrantData.quadrants).enter().append(\"g\").attr(\"class\", \"quadrant\");\n  quadrants.append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill);\n  quadrants.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text)).text((data) => data.text.text);\n  const labels = labelGroup.selectAll(\"g.label\").data(quadrantData.axisLabels).enter().append(\"g\").attr(\"class\", \"label\");\n  labels.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.horizontalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.verticalPos)).attr(\"transform\", (data) => getTransformation(data));\n  const dataPoints = dataPointGroup.selectAll(\"g.data-point\").data(quadrantData.points).enter().append(\"g\").attr(\"class\", \"data-point\");\n  dataPoints.append(\"circle\").attr(\"cx\", (data) => data.x).attr(\"cy\", (data) => data.y).attr(\"r\", (data) => data.radius).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeColor).attr(\"stroke-width\", (data) => data.strokeWidth);\n  dataPoints.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text.text).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text));\n}, \"draw\");\nvar quadrantRenderer_default = {\n  draw\n};\n\n// src/diagrams/quadrant-chart/quadrantDiagram.ts\nvar diagram = {\n  parser: quadrant_default,\n  db: quadrantDb_default,\n  renderer: quadrantRenderer_default,\n  styles: /* @__PURE__ */ __name(() => \"\", \"styles\")\n};\nexport {\n  diagram\n};\n"], "names": ["parser", "o", "__name", "k", "v", "o2", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "$Vv", "$Vw", "$Vx", "$Vy", "$Vz", "$VA", "$VB", "$VC", "$VD", "$VE", "$VF", "$VG", "$VH", "$VI", "$VJ", "$VK", "$VL", "$VM", "$VN", "$VO", "$VP", "$VQ", "$VR", "$VS", "$VT", "$VU", "$VV", "$VW", "$VX", "$VY", "$VZ", "$V_", "$V$", "parser2", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "TERROR", "EOF", "args", "lexer2", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "state", "action", "r", "yyval", "p", "len", "newState", "expected", "errStr", "lexer", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "<PERSON><PERSON><PERSON>", "quadrant_default", "defaultThemeVariables", "getThemeVariables", "QuadrantBuilder", "defaultConfig_default", "log", "data", "points", "className", "styles", "config2", "themeConfig", "xAxisPosition", "showXAxis", "showYAxis", "showTitle", "xAxisSpaceCalculation", "xAxisSpace", "yAxisSpaceCalculation", "yAxisSpace", "titleSpaceCalculation", "titleSpace", "quadrantLeft", "quadrantTop", "quadrantWidth", "quadrantHeight", "quadrantHalfWidth", "quadrantHalfHeight", "spaceData", "quadrantSpace", "drawXAxisLabelsInMiddle", "drawYAxisLabelsInMiddle", "axisLabels", "quadrants", "quadrant", "xAxis", "scaleLinear", "yAxis", "point", "classStyles", "halfExternalBorderWidth", "calculatedSpace", "InvalidStyleError", "style", "value", "type", "validateHexCode", "validateNumber", "validateSizeInPixels", "config", "getConfig", "textSanitizer", "text", "sanitizeText", "quadrantBuilder", "setQuadrant1Text", "textObj", "setQuadrant2Text", "setQuadrant3Text", "setQuadrant4Text", "setXAxisLeftText", "setXAxisRightText", "setYAxisTopText", "setYAxisBottomText", "parseStyles", "stylesObject", "key", "addPoint", "x", "y", "addClass", "<PERSON><PERSON><PERSON><PERSON>", "width", "setHeight", "height", "getQuadrantData", "themeVariables", "quadrantChartConfig", "getDiagramTitle", "clear2", "clear", "quadrantDb_default", "setAccTitle", "getAccTitle", "setDiagramTitle", "getAccDescription", "setAccDescription", "draw", "txt", "id", "_version", "diagObj", "getDominantBaseLine", "horizontalPos", "getTextAnchor", "verticalPos", "getTransformation", "conf", "securityLevel", "sandboxElement", "select", "svg", "group", "configureSvgSize", "quadrantData", "quadrantsGroup", "borderGroup", "dataPointGroup", "labelGroup", "titleGroup", "dataPoints", "quadrantRenderer_default", "diagram"], "mappings": "iXAkBA,IAAIA,GAAS,UAAW,CACtB,IAAIC,EAAoBC,EAAO,SAASC,EAAGC,EAAGC,EAAIC,EAAG,CACnD,IAAKD,EAAKA,GAAM,CAAE,EAAEC,EAAIH,EAAE,OAAQG,IAAKD,EAAGF,EAAEG,CAAC,CAAC,EAAIF,EAAG,CACrD,OAAOC,CACX,EAAK,GAAG,EAAGE,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,GAAG,EAAGC,GAAM,CAAC,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EACl0CC,GAAU,CACZ,MAAuBrE,EAAO,UAAiB,CAC9C,EAAE,OAAO,EACV,GAAI,CAAE,EACN,SAAU,CAAE,MAAS,EAAG,cAAiB,EAAG,MAAS,EAAG,IAAO,EAAG,YAAe,EAAG,KAAQ,EAAG,MAAS,EAAG,QAAW,EAAG,MAAS,GAAI,MAAS,GAAI,IAAO,GAAI,KAAQ,GAAI,KAAQ,GAAI,aAAgB,GAAI,eAAkB,GAAI,KAAQ,GAAI,MAAS,GAAI,MAAS,GAAI,IAAO,GAAI,SAAY,GAAI,MAAS,GAAI,UAAa,GAAI,kBAAqB,GAAI,SAAY,GAAI,MAAS,GAAI,IAAO,GAAI,SAAY,GAAI,SAAY,GAAI,KAAQ,GAAI,UAAa,GAAI,YAAe,GAAI,gBAAmB,GAAI,OAAU,GAAI,MAAS,GAAI,YAAe,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,QAAW,GAAI,KAAQ,GAAI,YAAe,GAAI,QAAW,GAAI,QAAW,GAAI,WAAc,GAAI,SAAU,GAAI,sBAAuB,GAAI,SAAU,GAAI,WAAc,GAAI,WAAc,GAAI,WAAc,GAAI,WAAc,GAAI,QAAW,GAAI,KAAQ,GAAI,IAAO,GAAI,cAAiB,GAAI,gBAAmB,GAAI,IAAO,GAAI,OAAU,GAAI,SAAY,GAAI,YAAe,GAAI,KAAQ,GAAI,OAAU,GAAI,IAAO,GAAI,WAAc,GAAI,QAAW,EAAG,KAAQ,CAAG,EACjkC,WAAY,CAAE,EAAG,QAAS,EAAG,QAAS,EAAG,MAAO,EAAG,cAAe,EAAG,OAAQ,EAAG,QAAS,EAAG,UAAW,GAAI,QAAS,GAAI,QAAS,GAAI,MAAO,GAAI,OAAQ,GAAI,OAAQ,GAAI,eAAgB,GAAI,OAAQ,GAAI,QAAS,GAAI,QAAS,GAAI,MAAO,GAAI,WAAY,GAAI,WAAY,GAAI,QAAS,GAAI,cAAe,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,UAAW,GAAI,cAAe,GAAI,UAAW,GAAI,UAAW,GAAI,aAAc,GAAI,SAAU,GAAI,sBAAuB,GAAI,SAAU,GAAI,aAAc,GAAI,aAAc,GAAI,aAAc,GAAI,aAAc,GAAI,UAAW,GAAI,OAAQ,GAAI,MAAO,GAAI,MAAO,GAAI,SAAU,GAAI,cAAe,GAAI,OAAQ,GAAI,SAAU,GAAI,MAAO,GAAI,YAAc,EACpvB,aAAc,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,CAAC,EAC/uB,cAA+BA,EAAO,SAAmBsE,EAAQC,EAAQC,EAAUC,EAAIC,EAASC,EAAIC,GAAI,CACtG,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAO,CACb,IAAK,IACH,KAAK,EAAIC,EAAGE,CAAE,EACd,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAAI,GAAKF,EAAGE,CAAE,EAChC,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAAIF,EAAGE,CAAE,EAC3B,MACF,IAAK,IACH,KAAK,EAAI,CAACF,EAAGE,CAAE,EAAE,KAAI,CAAE,EACvB,MACF,IAAK,IACHF,EAAGE,EAAK,CAAC,EAAE,KAAKF,EAAGE,CAAE,EAAE,KAAI,CAAE,EAC7B,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAClB,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAClBJ,EAAG,SAASE,EAAGE,EAAK,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAC9B,MACF,IAAK,IACH,KAAK,EAAI,GACT,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,CAAE,EAAE,KAAI,EACpBJ,EAAG,gBAAgB,KAAK,CAAC,EACzB,MACF,IAAK,IACH,KAAK,EAAIE,EAAGE,CAAE,EAAE,KAAI,EACpBJ,EAAG,YAAY,KAAK,CAAC,EACrB,MACF,IAAK,IACL,IAAK,IACH,KAAK,EAAIE,EAAGE,CAAE,EAAE,KAAI,EACpBJ,EAAG,kBAAkB,KAAK,CAAC,EAC3B,MACF,IAAK,IACHA,EAAG,WAAWE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EAC9B,KAAK,EAAIF,EAAGE,CAAE,EAAE,OAAO,CAAC,EACxB,MACF,IAAK,IACHJ,EAAG,SAASE,EAAGE,EAAK,CAAC,EAAG,GAAIF,EAAGE,EAAK,CAAC,EAAGF,EAAGE,CAAE,EAAG,CAAE,CAAA,EAClD,MACF,IAAK,IACHJ,EAAG,SAASE,EAAGE,EAAK,CAAC,EAAGF,EAAGE,EAAK,CAAC,EAAGF,EAAGE,EAAK,CAAC,EAAGF,EAAGE,CAAE,EAAG,CAAA,CAAE,EAC1D,MACF,IAAK,IACHJ,EAAG,SAASE,EAAGE,EAAK,CAAC,EAAG,GAAIF,EAAGE,EAAK,CAAC,EAAGF,EAAGE,EAAK,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAC1D,MACF,IAAK,IACHJ,EAAG,SAASE,EAAGE,EAAK,CAAC,EAAGF,EAAGE,EAAK,CAAC,EAAGF,EAAGE,EAAK,CAAC,EAAGF,EAAGE,EAAK,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAClE,MACF,IAAK,IACHJ,EAAG,iBAAiBE,EAAGE,EAAK,CAAC,CAAC,EAC9BJ,EAAG,kBAAkBE,EAAGE,CAAE,CAAC,EAC3B,MACF,IAAK,IACHF,EAAGE,EAAK,CAAC,EAAE,MAAQ,MACnBJ,EAAG,iBAAiBE,EAAGE,EAAK,CAAC,CAAC,EAC9B,MACF,IAAK,IACHJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACF,IAAK,IACHJ,EAAG,mBAAmBE,EAAGE,EAAK,CAAC,CAAC,EAChCJ,EAAG,gBAAgBE,EAAGE,CAAE,CAAC,EACzB,MACF,IAAK,IACHF,EAAGE,EAAK,CAAC,EAAE,MAAQ,MACnBJ,EAAG,mBAAmBE,EAAGE,EAAK,CAAC,CAAC,EAChC,MACF,IAAK,IACHJ,EAAG,mBAAmBE,EAAGE,CAAE,CAAC,EAC5B,MACF,IAAK,IACHJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACF,IAAK,IACHJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACF,IAAK,IACHJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACF,IAAK,IACHJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACF,IAAK,IACH,KAAK,EAAI,CAAE,KAAMF,EAAGE,CAAE,EAAG,KAAM,QAC/B,MACF,IAAK,IACH,KAAK,EAAI,CAAE,KAAMF,EAAGE,EAAK,CAAC,EAAE,KAAO,GAAKF,EAAGE,CAAE,EAAG,KAAMF,EAAGE,EAAK,CAAC,EAAE,MACjE,MACF,IAAK,IACH,KAAK,EAAI,CAAE,KAAMF,EAAGE,CAAE,EAAG,KAAM,QAC/B,MACF,IAAK,IACH,KAAK,EAAI,CAAE,KAAMF,EAAGE,CAAE,EAAG,KAAM,YAC/B,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAAI,GAAKF,EAAGE,CAAE,EAChC,KACH,CACF,EAAE,WAAW,EACd,MAAO,CAAC,CAAE,GAAIxE,EAAK,GAAI,EAAG,GAAI,EAAG,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAG,CAAC,CAAC,CAAC,EAAI,CAAE,GAAIJ,EAAK,GAAI,EAAG,GAAI,EAAG,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,EAAI,CAAE,GAAIJ,EAAK,GAAI,EAAG,GAAI,EAAG,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,EAAEV,EAAEW,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,EAAGX,EAAEY,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGZ,EAAEY,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGZ,EAAEY,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,EAAG,CAAC,EAAG,EAAE,CAAG,EAAE,CAAE,EAAG,CAAC,EAAG,EAAE,CAAC,EAAIZ,EAAEa,EAAKC,EAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,CAAC,EAAG,EAAE,EAAG,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,CAAA,EAAGxC,EAAEW,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,CAAG,EAAIV,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGb,EAAEa,EAAKC,EAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,CAAA,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGb,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGb,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAI,CAAE,GAAI,CAAC,EAAG,EAAE,CAAG,EAAE,CAAE,GAAI,CAAC,EAAG,EAAE,GAAKb,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGb,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,GAAK,CAAE,EAAGE,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,EAAE,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,EAAI,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,EAAI,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,EAAE,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAGzB,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,GAAI,GAAI,GAAI,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,EAAIxC,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAEW,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGX,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGb,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGb,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGb,EAAEa,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,EAAG,GAAI,EAAG+B,GAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAI,EAAE,EAAIvD,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAI,CAAC,EAAG,EAAE,EAAG,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAI,CAAC,EAAG,EAAE,EAAG,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,CAAA,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,CAAA,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,CAAA,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,GAAK,CAAE,GAAI,CAAC,EAAG,EAAE,CAAG,EAAExC,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG3C,EAAE2C,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,EAAG,GAAI,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAI,CAAC,EAAG,EAAE,CAAC,EAAIvD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,GAAK,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAI,CAAE,EAAGiB,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAI,GAAI,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAI,GAAI,GAAI,EAAE,EAAIlE,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAGxC,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,EAAG4C,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAG,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAG,EAAElE,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAIsD,GAAK,EAAGnE,EAAEoE,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,IAAK,EAAGX,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAK,CAAA,EAAGlE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAIsD,EAAG,CAAE,EAAGnE,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,EAAG4C,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAK,CAAA,EAAG,CAAE,EAAGT,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAI,GAAI,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAI,GAAK,EAAElE,EAAEqE,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGrE,EAAEa,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAIsD,GAAK,EAAGnE,EAAEoE,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,IAAK,EAAGX,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAG,CAAE,CAAC,EAC/gL,eAAgB,CAAE,EAAG,CAAC,EAAG,EAAE,EAAG,EAAG,CAAC,EAAG,EAAE,CAAG,EAC1C,WAA4BjE,EAAO,SAAoB8E,EAAKC,EAAM,CAChE,GAAIA,EAAK,YACP,KAAK,MAAMD,CAAG,MACT,CACL,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACP,CACF,EAAE,YAAY,EACf,MAAuBhF,EAAO,SAAeiF,EAAO,CAC/C,IAACC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAE,EAAEC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAE,EAAEC,GAAQ,KAAK,MAAOjB,EAAS,GAAIE,GAAW,EAAGD,GAAS,EAAmBiB,GAAS,EAAGC,GAAM,EAClKC,GAAOJ,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCK,EAAS,OAAO,OAAO,KAAK,KAAK,EACjCC,EAAc,CAAE,GAAI,CAAA,GACxB,QAAS3F,MAAK,KAAK,GACb,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IACjD2F,EAAY,GAAG3F,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGjC0F,EAAO,SAASV,EAAOW,EAAY,EAAE,EACrCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAO,OAAU,MAC1BA,EAAO,OAAS,IAElB,IAAIE,GAAQF,EAAO,OACnBL,EAAO,KAAKO,EAAK,EACjB,IAAIC,GAASH,EAAO,SAAWA,EAAO,QAAQ,OAC1C,OAAOC,EAAY,GAAG,YAAe,WACvC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAEhD,SAASG,GAASC,EAAG,CACnBb,EAAM,OAASA,EAAM,OAAS,EAAIa,EAClCX,EAAO,OAASA,EAAO,OAASW,EAChCV,EAAO,OAASA,EAAO,OAASU,CACjC,CACDhG,EAAO+F,GAAU,UAAU,EAC3B,SAASE,IAAM,CACb,IAAIC,EACJ,OAAAA,EAAQd,EAAO,IAAG,GAAMO,EAAO,IAAK,GAAIF,GACpC,OAAOS,GAAU,WACfA,aAAiB,QACnBd,EAASc,EACTA,EAAQd,EAAO,OAEjBc,EAAQhB,EAAK,SAASgB,CAAK,GAAKA,GAE3BA,CACR,CACDlG,EAAOiG,GAAK,KAAK,EAEjB,QADIE,EAAwBC,EAAOC,EAAWC,GAAGC,GAAQ,CAAE,EAAEC,GAAGC,EAAKC,GAAUC,KAClE,CAUX,GATAP,EAAQjB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAeiB,CAAK,EAC3BC,EAAS,KAAK,eAAeD,CAAK,IAE9BD,IAAW,MAAQ,OAAOA,EAAU,OACtCA,EAASF,GAAG,GAEdI,EAASd,GAAMa,CAAK,GAAKb,GAAMa,CAAK,EAAED,CAAM,GAE1C,OAAOE,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CACjE,IAAIO,GAAS,GACbD,GAAW,CAAA,EACX,IAAKH,MAAKjB,GAAMa,CAAK,EACf,KAAK,WAAWI,EAAC,GAAKA,GAAIhB,IAC5BmB,GAAS,KAAK,IAAM,KAAK,WAAWH,EAAC,EAAI,GAAG,EAG5Cb,EAAO,aACTiB,GAAS,wBAA0BpC,GAAW,GAAK;AAAA,EAAQmB,EAAO,aAAc,EAAG;AAAA,YAAiBgB,GAAS,KAAK,IAAI,EAAI,WAAa,KAAK,WAAWR,CAAM,GAAKA,GAAU,IAE5KS,GAAS,wBAA0BpC,GAAW,GAAK,iBAAmB2B,GAAUV,GAAM,eAAiB,KAAO,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAErJ,KAAK,WAAWS,GAAQ,CACtB,KAAMjB,EAAO,MACb,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAO,SACb,IAAKE,GACL,SAAAc,EACZ,CAAW,CACF,CACD,GAAIN,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAChD,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcD,CAAM,EAEpG,OAAQE,EAAO,CAAC,EAAC,CACf,IAAK,GACHlB,EAAM,KAAKgB,CAAM,EACjBd,EAAO,KAAKM,EAAO,MAAM,EACzBL,EAAO,KAAKK,EAAO,MAAM,EACzBR,EAAM,KAAKkB,EAAO,CAAC,CAAC,EACpBF,EAAS,KAEP5B,GAASoB,EAAO,OAChBrB,EAASqB,EAAO,OAChBnB,GAAWmB,EAAO,SAClBE,GAAQF,EAAO,OAQjB,MACF,IAAK,GAwBH,GAvBAc,EAAM,KAAK,aAAaJ,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCE,GAAM,EAAIlB,EAAOA,EAAO,OAASoB,CAAG,EACpCF,GAAM,GAAK,CACT,WAAYjB,EAAOA,EAAO,QAAUmB,GAAO,EAAE,EAAE,WAC/C,UAAWnB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUmB,GAAO,EAAE,EAAE,aACjD,YAAanB,EAAOA,EAAO,OAAS,CAAC,EAAE,WACrD,EACgBQ,KACFS,GAAM,GAAG,MAAQ,CACfjB,EAAOA,EAAO,QAAUmB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CnB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACjD,GAEYgB,GAAI,KAAK,cAAc,MAAMC,GAAO,CAClCjC,EACAC,GACAC,GACAoB,EAAY,GACZS,EAAO,CAAC,EACRhB,EACAC,CACd,EAAc,OAAOI,EAAI,CAAC,EACV,OAAOY,GAAM,IACf,OAAOA,GAELG,IACFtB,EAAQA,EAAM,MAAM,EAAG,GAAKsB,EAAM,CAAC,EACnCpB,EAASA,EAAO,MAAM,EAAG,GAAKoB,CAAG,EACjCnB,EAASA,EAAO,MAAM,EAAG,GAAKmB,CAAG,GAEnCtB,EAAM,KAAK,KAAK,aAAakB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ChB,EAAO,KAAKkB,GAAM,CAAC,EACnBjB,EAAO,KAAKiB,GAAM,EAAE,EACpBG,GAAWnB,GAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAKuB,EAAQ,EACnB,MACF,IAAK,GACH,MAAO,EACV,CACF,CACD,MAAO,EACR,EAAE,OAAO,CACd,EACMG,GAAwB,UAAW,CACrC,IAAIlB,EAAS,CACX,IAAK,EACL,WAA4B3F,EAAO,SAAoB8E,EAAKC,EAAM,CAChE,GAAI,KAAK,GAAG,OACV,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAEtB,EAAE,YAAY,EAEf,SAA0B9E,EAAO,SAASiF,EAAOR,EAAI,CACnD,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAA,EAC3B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACZ,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACvB,EACY,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,EAAG,CAAC,GAE3B,KAAK,OAAS,EACP,IACR,EAAE,UAAU,EAEb,MAAuBjF,EAAO,UAAW,CACvC,IAAI8G,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACF,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEV,KAAK,QAAQ,QACf,KAAK,OAAO,MAAM,CAAC,IAErB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACR,EAAE,OAAO,EAEV,MAAuB9G,EAAO,SAAS8G,EAAI,CACzC,IAAIL,EAAMK,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EACpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASL,CAAG,EAC5D,KAAK,QAAUA,EACf,IAAIO,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EACzDD,EAAM,OAAS,IACjB,KAAK,UAAYA,EAAM,OAAS,GAElC,IAAIT,EAAI,KAAK,OAAO,MACpB,YAAK,OAAS,CACZ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaS,GAASA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAAKA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAAS,KAAK,OAAO,aAAeN,CAClM,EACY,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAErD,KAAK,OAAS,KAAK,OAAO,OACnB,IACR,EAAE,OAAO,EAEV,KAAsBzG,EAAO,UAAW,CACtC,YAAK,MAAQ,GACN,IACR,EAAE,MAAM,EAET,OAAwBA,EAAO,UAAW,CACxC,GAAI,KAAK,QAAQ,gBACf,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,eAAgB,CAChO,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACvB,CAAW,EAEH,OAAO,IACR,EAAE,QAAQ,EAEX,KAAsBA,EAAO,SAASgG,EAAG,CACvC,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAC/B,EAAE,MAAM,EAET,UAA2BhG,EAAO,UAAW,CAC3C,IAAIiH,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAQ,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC5E,EAAE,WAAW,EAEd,cAA+BjH,EAAO,UAAW,CAC/C,IAAIkH,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KAChBA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAKA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAG,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAChF,EAAE,eAAe,EAElB,aAA8BlH,EAAO,UAAW,CAC9C,IAAImH,EAAM,KAAK,YACXC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAe,EAAG;AAAA,EAAOC,EAAI,GAChD,EAAE,cAAc,EAEjB,WAA4BpH,EAAO,SAASqH,EAAOC,EAAc,CAC/D,IAAIpB,EAAOa,EAAOQ,EAmDlB,GAlDI,KAAK,QAAQ,kBACfA,EAAS,CACP,SAAU,KAAK,SACf,OAAQ,CACN,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC1B,EACD,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACvB,EACc,KAAK,QAAQ,SACfA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAGnDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACF,KAAK,UAAYA,EAAM,QAEzB,KAAK,OAAS,CACZ,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EAAQA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAAS,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACvJ,EACQ,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAE9D,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBnB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMoB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SACpB,KAAK,KAAO,IAEVpB,EACF,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1B,QAASjG,KAAKsH,EACZ,KAAKtH,CAAC,EAAIsH,EAAOtH,CAAC,EAEpB,MAAO,EACR,CACD,MAAO,EACR,EAAE,YAAY,EAEf,KAAsBD,EAAO,UAAW,CACtC,GAAI,KAAK,KACP,OAAO,KAAK,IAET,KAAK,SACR,KAAK,KAAO,IAEd,IAAIkG,EAAOmB,EAAOG,EAAWC,EACxB,KAAK,QACR,KAAK,OAAS,GACd,KAAK,MAAQ,IAGf,QADIC,EAAQ,KAAK,gBACRC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAEhC,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGzD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAEhC,GADAzB,EAAQ,KAAK,WAAWsB,EAAWE,EAAMC,CAAC,CAAC,EACvCzB,IAAU,GACZ,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1BmB,EAAQ,GACR,QAChB,KACgB,OAAO,EAEV,SAAU,CAAC,KAAK,QAAQ,KACvB,MAIN,OAAIA,GACFnB,EAAQ,KAAK,WAAWmB,EAAOK,EAAMD,CAAK,CAAC,EACvCvB,IAAU,GACLA,EAEF,IAEL,KAAK,SAAW,GACX,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,eAAgB,CACtH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACvB,CAAW,CAEJ,EAAE,MAAM,EAET,IAAqBlG,EAAO,UAAe,CACzC,IAAIsG,EAAI,KAAK,OACb,OAAIA,GAGK,KAAK,KAEf,EAAE,KAAK,EAER,MAAuBtG,EAAO,SAAe4H,EAAW,CACtD,KAAK,eAAe,KAAKA,CAAS,CACnC,EAAE,OAAO,EAEV,SAA0B5H,EAAO,UAAoB,CACnD,IAAIgG,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACC,KAAK,eAAe,MAEpB,KAAK,eAAe,CAAC,CAE/B,EAAE,UAAU,EAEb,cAA+BhG,EAAO,UAAyB,CAC7D,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAC3E,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAErC,EAAE,eAAe,EAElB,SAA0BA,EAAO,SAAkBgG,EAAG,CAEpD,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACA,KAAK,eAAeA,CAAC,EAErB,SAEV,EAAE,UAAU,EAEb,UAA2BhG,EAAO,SAAmB4H,EAAW,CAC9D,KAAK,MAAMA,CAAS,CACrB,EAAE,WAAW,EAEd,eAAgC5H,EAAO,UAA0B,CAC/D,OAAO,KAAK,eAAe,MAC5B,EAAE,gBAAgB,EACnB,QAAS,CAAE,mBAAoB,EAAM,EACrC,cAA+BA,EAAO,SAAmByE,EAAIoD,EAAKC,EAA2BC,EAAU,CAErG,OAAQD,EAAyB,CAC/B,IAAK,GACH,MACF,IAAK,GACH,MACF,IAAK,GACH,MAAO,IAET,IAAK,GACH,MACF,IAAK,GACH,YAAK,MAAM,OAAO,EACX,GAET,IAAK,GACH,YAAK,SAAQ,EACN,cAET,IAAK,GACH,YAAK,MAAM,WAAW,EACf,GAET,IAAK,GACH,YAAK,SAAQ,EACN,kBAET,IAAK,GACH,YAAK,MAAM,WAAW,EACf,GAET,IAAK,GACH,YAAK,SAAQ,EACN,kBAET,IAAK,IACH,KAAK,MAAM,qBAAqB,EAChC,MACF,IAAK,IACH,KAAK,SAAQ,EACb,MACF,IAAK,IACH,MAAO,4BAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,KAAK,MAAM,WAAW,EACtB,MACF,IAAK,IACH,MAAO,SAET,IAAK,IACH,KAAK,SAAQ,EACb,MACF,IAAK,IACH,KAAK,MAAM,QAAQ,EACnB,MACF,IAAK,IACH,KAAK,SAAQ,EACb,MACF,IAAK,IACH,MAAO,MAET,IAAK,IACH,KAAK,MAAM,YAAY,EACvB,MACF,IAAK,IACH,YAAK,SAAQ,EACN,GAET,IAAK,IACH,YAAK,MAAM,aAAa,EACjB,GAET,IAAK,IACH,YAAK,MAAM,SAAS,EACb,GAET,IAAK,IACH,KAAK,SAAQ,EACb,MACF,IAAK,IACH,KAAK,SAAQ,EACb,KAAK,MAAM,SAAS,EACpB,MACF,IAAK,IACH,YAAK,SAAQ,EACN,GAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,GAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,GAET,IAAK,IACH,MAAO,GAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,GAEV,CACF,EAAE,WAAW,EACd,MAAO,CAAC,uBAAwB,sBAAuB,gBAAiB,iBAAkB,gBAAiB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,yBAA0B,aAAc,eAAgB,mBAAoB,mBAAoB,iBAAkB,uBAAwB,uBAAwB,uBAAwB,uBAAwB,mBAAoB,eAAgB,eAAgB,eAAgB,YAAa,YAAa,cAAe,YAAa,aAAc,qBAAsB,uBAAwB,gBAAiB,gBAAiB,uBAAwB,0BAA2B,kBAAmB,UAAW,WAAY,UAAW,UAAW,UAAW,WAAY,UAAW,aAAc,WAAY,UAAW,UAAW,eAAgB,WAAY,UAAW,6BAA8B,SAAS,EACr6B,WAAY,CAAE,WAAc,CAAE,MAAS,CAAC,EAAE,EAAG,UAAa,EAAK,EAAI,QAAW,CAAE,MAAS,CAAC,EAAE,EAAG,UAAa,EAAO,EAAE,QAAW,CAAE,MAAS,CAAC,EAAE,EAAG,UAAa,IAAS,YAAe,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAO,EAAE,oBAAuB,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAK,EAAI,UAAa,CAAE,MAAS,CAAC,CAAC,EAAG,UAAa,EAAO,EAAE,UAAa,CAAE,MAAS,CAAC,CAAC,EAAG,UAAa,EAAO,EAAE,MAAS,CAAE,MAAS,CAAC,CAAC,EAAG,UAAa,EAAO,EAAE,UAAa,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAK,EAAI,OAAU,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,IAAS,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAI,CAAI,CACzuB,EACI,OAAOnC,CACX,IACEtB,GAAQ,MAAQwC,GAChB,SAASmB,IAAS,CAChB,KAAK,GAAK,EACX,CACD,OAAAhI,EAAOgI,GAAQ,QAAQ,EACvBA,GAAO,UAAY3D,GACnBA,GAAQ,OAAS2D,GACV,IAAIA,EACb,IACAlI,GAAO,OAASA,GAChB,IAAImI,GAAmBnI,GAInBoI,EAAwBC,GAAiB,EACzCC,GAAkB,KAAM,CAC1B,aAAc,CACZ,KAAK,QAA0B,IAAI,IACnC,KAAK,OAAS,KAAK,mBACnB,KAAK,YAAc,KAAK,wBACxB,KAAK,KAAO,KAAK,gBAClB,CACD,MAAA,CACEpI,EAAO,KAAM,iBAAiB,CAC/B,CACD,gBAAiB,CACf,MAAO,CACL,UAAW,GACX,cAAe,GACf,cAAe,GACf,cAAe,GACf,cAAe,GACf,cAAe,GACf,eAAgB,GAChB,gBAAiB,GACjB,aAAc,GACd,OAAQ,CAAE,CAChB,CACG,CACD,kBAAmB,CACjB,MAAO,CACL,UAAW,GACX,UAAW,GACX,UAAW,GACX,YAAaqI,EAAsB,eAAe,YAAc,IAChE,WAAYA,EAAsB,eAAe,aAAe,IAChE,aAAcA,EAAsB,eAAe,cAAgB,GACnE,cAAeA,EAAsB,eAAe,eAAiB,GACrE,gBAAiBA,EAAsB,eAAe,iBAAmB,EACzE,kBAAmBA,EAAsB,eAAe,mBAAqB,EAC7E,kBAAmBA,EAAsB,eAAe,mBAAqB,EAC7E,mBAAoBA,EAAsB,eAAe,oBAAsB,GAC/E,mBAAoBA,EAAsB,eAAe,oBAAsB,GAC/E,sBAAuBA,EAAsB,eAAe,uBAAyB,GACrF,uBAAwBA,EAAsB,eAAe,wBAA0B,EACvF,iBAAkBA,EAAsB,eAAe,kBAAoB,EAC3E,mBAAoBA,EAAsB,eAAe,oBAAsB,GAC/E,YAAaA,EAAsB,eAAe,aAAe,EACjE,cAAeA,EAAsB,eAAe,eAAiB,MACrE,cAAeA,EAAsB,eAAe,eAAiB,OACrE,kCAAmCA,EAAsB,eAAe,mCAAqC,EAC7G,kCAAmCA,EAAsB,eAAe,mCAAqC,CACnH,CACG,CACD,uBAAwB,CACtB,MAAO,CACL,cAAeH,EAAsB,cACrC,cAAeA,EAAsB,cACrC,cAAeA,EAAsB,cACrC,cAAeA,EAAsB,cACrC,kBAAmBA,EAAsB,kBACzC,kBAAmBA,EAAsB,kBACzC,kBAAmBA,EAAsB,kBACzC,kBAAmBA,EAAsB,kBACzC,kBAAmBA,EAAsB,kBACzC,sBAAuBA,EAAsB,sBAC7C,sBAAuBA,EAAsB,sBAC7C,sBAAuBA,EAAsB,sBAC7C,kBAAmBA,EAAsB,kBACzC,iCAAkCA,EAAsB,iCACxD,iCAAkCA,EAAsB,gCAC9D,CACG,CACD,OAAQ,CACN,KAAK,OAAS,KAAK,mBACnB,KAAK,YAAc,KAAK,wBACxB,KAAK,KAAO,KAAK,iBACjB,KAAK,QAA0B,IAAI,IACnCI,GAAI,KAAK,cAAc,CACxB,CACD,QAAQC,EAAM,CACZ,KAAK,KAAO,CAAE,GAAG,KAAK,KAAM,GAAGA,EAChC,CACD,UAAUC,EAAQ,CAChB,KAAK,KAAK,OAAS,CAAC,GAAGA,EAAQ,GAAG,KAAK,KAAK,MAAM,CACnD,CACD,SAASC,EAAWC,EAAQ,CAC1B,KAAK,QAAQ,IAAID,EAAWC,CAAM,CACnC,CACD,UAAUC,EAAS,CACjBL,GAAI,MAAM,0BAA2BK,CAAO,EAC5C,KAAK,OAAS,CAAE,GAAG,KAAK,OAAQ,GAAGA,EACpC,CACD,eAAeC,EAAa,CAC1BN,GAAI,MAAM,+BAAgCM,CAAW,EACrD,KAAK,YAAc,CAAE,GAAG,KAAK,YAAa,GAAGA,EAC9C,CACD,eAAeC,EAAeC,EAAWC,EAAWC,EAAW,CAC7D,MAAMC,EAAwB,KAAK,OAAO,kBAAoB,EAAI,KAAK,OAAO,mBACxEC,EAAa,CACjB,IAAKL,IAAkB,OAASC,EAAYG,EAAwB,EACpE,OAAQJ,IAAkB,UAAYC,EAAYG,EAAwB,CAChF,EACUE,EAAwB,KAAK,OAAO,kBAAoB,EAAI,KAAK,OAAO,mBACxEC,EAAa,CACjB,KAAM,KAAK,OAAO,gBAAkB,QAAUL,EAAYI,EAAwB,EAClF,MAAO,KAAK,OAAO,gBAAkB,SAAWJ,EAAYI,EAAwB,CAC1F,EACUE,EAAwB,KAAK,OAAO,cAAgB,KAAK,OAAO,aAAe,EAC/EC,EAAa,CACjB,IAAKN,EAAYK,EAAwB,CAC/C,EACUE,EAAe,KAAK,OAAO,gBAAkBH,EAAW,KACxDI,EAAc,KAAK,OAAO,gBAAkBN,EAAW,IAAMI,EAAW,IACxEG,EAAgB,KAAK,OAAO,WAAa,KAAK,OAAO,gBAAkB,EAAIL,EAAW,KAAOA,EAAW,MACxGM,EAAiB,KAAK,OAAO,YAAc,KAAK,OAAO,gBAAkB,EAAIR,EAAW,IAAMA,EAAW,OAASI,EAAW,IAC7HK,EAAoBF,EAAgB,EACpCG,EAAqBF,EAAiB,EAS5C,MAAO,CACL,WAAAR,EACA,WAAAE,EACA,WAAAE,EACA,cAZoB,CACpB,aAAAC,EACA,YAAAC,EACA,cAAAC,EACA,kBAAAE,EACA,eAAAD,EACA,mBAAAE,CACN,CAMA,CACG,CACD,cAAcf,EAAeC,EAAWC,EAAWc,EAAW,CAC5D,KAAM,CAAE,cAAAC,EAAe,WAAAR,CAAY,EAAGO,EAChC,CACJ,mBAAAD,EACA,eAAAF,EACA,aAAAH,EACA,kBAAAI,EACA,YAAAH,EACA,cAAAC,CACD,EAAGK,EACEC,EAA0B,EAAQ,KAAK,KAAK,eAC5CC,EAA0B,EAAQ,KAAK,KAAK,aAC5CC,EAAa,CAAA,EACnB,OAAI,KAAK,KAAK,eAAiBnB,GAC7BmB,EAAW,KAAK,CACd,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,sBACvB,EAAGV,GAAgBQ,EAA0BJ,EAAoB,EAAI,GACrE,EAAGd,IAAkB,MAAQ,KAAK,OAAO,kBAAoBS,EAAW,IAAM,KAAK,OAAO,kBAAoBE,EAAcE,EAAiB,KAAK,OAAO,gBACzJ,SAAU,KAAK,OAAO,mBACtB,YAAaK,EAA0B,SAAW,OAClD,cAAe,MACf,SAAU,CAClB,CAAO,EAEC,KAAK,KAAK,gBAAkBjB,GAC9BmB,EAAW,KAAK,CACd,KAAM,KAAK,KAAK,eAChB,KAAM,KAAK,YAAY,sBACvB,EAAGV,EAAeI,GAAqBI,EAA0BJ,EAAoB,EAAI,GACzF,EAAGd,IAAkB,MAAQ,KAAK,OAAO,kBAAoBS,EAAW,IAAM,KAAK,OAAO,kBAAoBE,EAAcE,EAAiB,KAAK,OAAO,gBACzJ,SAAU,KAAK,OAAO,mBACtB,YAAaK,EAA0B,SAAW,OAClD,cAAe,MACf,SAAU,CAClB,CAAO,EAEC,KAAK,KAAK,iBAAmBhB,GAC/BkB,EAAW,KAAK,CACd,KAAM,KAAK,KAAK,gBAChB,KAAM,KAAK,YAAY,sBACvB,EAAG,KAAK,OAAO,gBAAkB,OAAS,KAAK,OAAO,kBAAoB,KAAK,OAAO,kBAAoBV,EAAeE,EAAgB,KAAK,OAAO,gBACrJ,EAAGD,EAAcE,GAAkBM,EAA0BJ,EAAqB,EAAI,GACtF,SAAU,KAAK,OAAO,mBACtB,YAAaI,EAA0B,SAAW,OAClD,cAAe,MACf,SAAU,GAClB,CAAO,EAEC,KAAK,KAAK,cAAgBjB,GAC5BkB,EAAW,KAAK,CACd,KAAM,KAAK,KAAK,aAChB,KAAM,KAAK,YAAY,sBACvB,EAAG,KAAK,OAAO,gBAAkB,OAAS,KAAK,OAAO,kBAAoB,KAAK,OAAO,kBAAoBV,EAAeE,EAAgB,KAAK,OAAO,gBACrJ,EAAGD,EAAcI,GAAsBI,EAA0BJ,EAAqB,EAAI,GAC1F,SAAU,KAAK,OAAO,mBACtB,YAAaI,EAA0B,SAAW,OAClD,cAAe,MACf,SAAU,GAClB,CAAO,EAEIC,CACR,CACD,aAAaJ,EAAW,CACtB,KAAM,CAAE,cAAAC,CAAe,EAAGD,EACpB,CAAE,mBAAAD,EAAoB,aAAAL,EAAc,kBAAAI,EAAmB,YAAAH,CAAW,EAAKM,EACvEI,EAAY,CAChB,CACE,KAAM,CACJ,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,kBACvB,EAAG,EACH,EAAG,EACH,SAAU,KAAK,OAAO,sBACtB,YAAa,SACb,cAAe,SACf,SAAU,CACX,EACD,EAAGX,EAAeI,EAClB,EAAGH,EACH,MAAOG,EACP,OAAQC,EACR,KAAM,KAAK,YAAY,aACxB,EACD,CACE,KAAM,CACJ,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,kBACvB,EAAG,EACH,EAAG,EACH,SAAU,KAAK,OAAO,sBACtB,YAAa,SACb,cAAe,SACf,SAAU,CACX,EACD,EAAGL,EACH,EAAGC,EACH,MAAOG,EACP,OAAQC,EACR,KAAM,KAAK,YAAY,aACxB,EACD,CACE,KAAM,CACJ,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,kBACvB,EAAG,EACH,EAAG,EACH,SAAU,KAAK,OAAO,sBACtB,YAAa,SACb,cAAe,SACf,SAAU,CACX,EACD,EAAGL,EACH,EAAGC,EAAcI,EACjB,MAAOD,EACP,OAAQC,EACR,KAAM,KAAK,YAAY,aACxB,EACD,CACE,KAAM,CACJ,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,kBACvB,EAAG,EACH,EAAG,EACH,SAAU,KAAK,OAAO,sBACtB,YAAa,SACb,cAAe,SACf,SAAU,CACX,EACD,EAAGL,EAAeI,EAClB,EAAGH,EAAcI,EACjB,MAAOD,EACP,OAAQC,EACR,KAAM,KAAK,YAAY,aACxB,CACP,EACI,UAAWO,KAAYD,EACrBC,EAAS,KAAK,EAAIA,EAAS,EAAIA,EAAS,MAAQ,EAC5C,KAAK,KAAK,OAAO,SAAW,GAC9BA,EAAS,KAAK,EAAIA,EAAS,EAAIA,EAAS,OAAS,EACjDA,EAAS,KAAK,cAAgB,WAE9BA,EAAS,KAAK,EAAIA,EAAS,EAAI,KAAK,OAAO,uBAC3CA,EAAS,KAAK,cAAgB,OAGlC,OAAOD,CACR,CACD,kBAAkBL,EAAW,CAC3B,KAAM,CAAE,cAAAC,CAAe,EAAGD,EACpB,CAAE,eAAAH,EAAgB,aAAAH,EAAc,YAAAC,EAAa,cAAAC,CAAa,EAAKK,EAC/DM,EAAQC,GAAW,EAAG,OAAO,CAAC,EAAG,CAAC,CAAC,EAAE,MAAM,CAACd,EAAcE,EAAgBF,CAAY,CAAC,EACvFe,EAAQD,GAAW,EAAG,OAAO,CAAC,EAAG,CAAC,CAAC,EAAE,MAAM,CAACX,EAAiBF,EAAaA,CAAW,CAAC,EA0B5F,OAzBe,KAAK,KAAK,OAAO,IAAKe,GAAU,CAC7C,MAAMC,EAAc,KAAK,QAAQ,IAAID,EAAM,SAAS,EACpD,OAAIC,IACFD,EAAQ,CAAE,GAAGC,EAAa,GAAGD,CAAK,GAEtB,CACZ,EAAGH,EAAMG,EAAM,CAAC,EAChB,EAAGD,EAAMC,EAAM,CAAC,EAChB,KAAMA,EAAM,OAAS,KAAK,YAAY,kBACtC,OAAQA,EAAM,QAAU,KAAK,OAAO,YACpC,KAAM,CACJ,KAAMA,EAAM,KACZ,KAAM,KAAK,YAAY,sBACvB,EAAGH,EAAMG,EAAM,CAAC,EAChB,EAAGD,EAAMC,EAAM,CAAC,EAAI,KAAK,OAAO,iBAChC,YAAa,SACb,cAAe,MACf,SAAU,KAAK,OAAO,mBACtB,SAAU,CACX,EACD,YAAaA,EAAM,aAAe,KAAK,YAAY,kBACnD,YAAaA,EAAM,aAAe,KAC1C,CAEA,CAAK,CAEF,CACD,WAAWV,EAAW,CACpB,MAAMY,EAA0B,KAAK,OAAO,kCAAoC,EAC1E,CAAE,cAAAX,CAAe,EAAGD,EACpB,CACJ,mBAAAD,EACA,eAAAF,EACA,aAAAH,EACA,kBAAAI,EACA,YAAAH,EACA,cAAAC,CACD,EAAGK,EAyDJ,MAxDoB,CAElB,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIP,EAAekB,EACnB,GAAIjB,EACJ,GAAID,EAAeE,EAAgBgB,EACnC,GAAIjB,CACL,EAED,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAID,EAAeE,EACnB,GAAID,EAAciB,EAClB,GAAIlB,EAAeE,EACnB,GAAID,EAAcE,EAAiBe,CACpC,EAED,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIlB,EAAekB,EACnB,GAAIjB,EAAcE,EAClB,GAAIH,EAAeE,EAAgBgB,EACnC,GAAIjB,EAAcE,CACnB,EAED,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIH,EACJ,GAAIC,EAAciB,EAClB,GAAIlB,EACJ,GAAIC,EAAcE,EAAiBe,CACpC,EAED,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIlB,EAAeI,EACnB,GAAIH,EAAciB,EAClB,GAAIlB,EAAeI,EACnB,GAAIH,EAAcE,EAAiBe,CACpC,EAED,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIlB,EAAekB,EACnB,GAAIjB,EAAcI,EAClB,GAAIL,EAAeE,EAAgBgB,EACnC,GAAIjB,EAAcI,CACnB,CACP,CAEG,CACD,SAASZ,EAAW,CAClB,GAAIA,EACF,MAAO,CACL,KAAM,KAAK,KAAK,UAChB,KAAM,KAAK,YAAY,kBACvB,SAAU,KAAK,OAAO,cACtB,cAAe,MACf,YAAa,SACb,SAAU,EACV,EAAG,KAAK,OAAO,aACf,EAAG,KAAK,OAAO,WAAa,CACpC,CAGG,CACD,OAAQ,CACN,MAAMF,EAAY,KAAK,OAAO,WAAa,CAAC,EAAE,KAAK,KAAK,eAAiB,KAAK,KAAK,gBAC7EC,EAAY,KAAK,OAAO,WAAa,CAAC,EAAE,KAAK,KAAK,cAAgB,KAAK,KAAK,iBAC5EC,EAAY,KAAK,OAAO,WAAa,CAAC,CAAC,KAAK,KAAK,UACjDH,EAAgB,KAAK,KAAK,OAAO,OAAS,EAAI,SAAW,KAAK,OAAO,cACrE6B,EAAkB,KAAK,eAAe7B,EAAeC,EAAWC,EAAWC,CAAS,EAC1F,MAAO,CACL,OAAQ,KAAK,kBAAkB0B,CAAe,EAC9C,UAAW,KAAK,aAAaA,CAAe,EAC5C,WAAY,KAAK,cAAc7B,EAAeC,EAAWC,EAAW2B,CAAe,EACnF,YAAa,KAAK,WAAWA,CAAe,EAC5C,MAAO,KAAK,SAAS1B,CAAS,CACpC,CACG,CACH,EAGI2B,GAAoB,cAAc,KAAM,CAC1C,MAAA,CACE3K,EAAO,KAAM,mBAAmB,CACjC,CACD,YAAY4K,EAAOC,EAAOC,EAAM,CAC9B,MAAM,aAAaF,CAAK,IAAIC,CAAK,mCAAmCC,CAAI,EAAE,EAC1E,KAAK,KAAO,mBACb,CACH,EACA,SAASC,GAAgBF,EAAO,CAC9B,MAAO,CAAC,oCAAoC,KAAKA,CAAK,CACxD,CACA7K,EAAO+K,GAAiB,iBAAiB,EACzC,SAASC,GAAeH,EAAO,CAC7B,MAAO,CAAC,QAAQ,KAAKA,CAAK,CAC5B,CACA7K,EAAOgL,GAAgB,gBAAgB,EACvC,SAASC,GAAqBJ,EAAO,CACnC,MAAO,CAAC,UAAU,KAAKA,CAAK,CAC9B,CACA7K,EAAOiL,GAAsB,sBAAsB,EAGnD,IAAIC,GAASC,GAAS,EACtB,SAASC,EAAcC,EAAM,CAC3B,OAAOC,GAAaD,EAAK,KAAM,EAAEH,EAAM,CACzC,CACAlL,EAAOoL,EAAe,eAAe,EACrC,IAAIG,EAAkB,IAAInD,GAC1B,SAASoD,GAAiBC,EAAS,CACjCF,EAAgB,QAAQ,CAAE,cAAeH,EAAcK,EAAQ,IAAI,CAAC,CAAE,CACxE,CACAzL,EAAOwL,GAAkB,kBAAkB,EAC3C,SAASE,GAAiBD,EAAS,CACjCF,EAAgB,QAAQ,CAAE,cAAeH,EAAcK,EAAQ,IAAI,CAAC,CAAE,CACxE,CACAzL,EAAO0L,GAAkB,kBAAkB,EAC3C,SAASC,GAAiBF,EAAS,CACjCF,EAAgB,QAAQ,CAAE,cAAeH,EAAcK,EAAQ,IAAI,CAAC,CAAE,CACxE,CACAzL,EAAO2L,GAAkB,kBAAkB,EAC3C,SAASC,GAAiBH,EAAS,CACjCF,EAAgB,QAAQ,CAAE,cAAeH,EAAcK,EAAQ,IAAI,CAAC,CAAE,CACxE,CACAzL,EAAO4L,GAAkB,kBAAkB,EAC3C,SAASC,GAAiBJ,EAAS,CACjCF,EAAgB,QAAQ,CAAE,cAAeH,EAAcK,EAAQ,IAAI,CAAC,CAAE,CACxE,CACAzL,EAAO6L,GAAkB,kBAAkB,EAC3C,SAASC,GAAkBL,EAAS,CAClCF,EAAgB,QAAQ,CAAE,eAAgBH,EAAcK,EAAQ,IAAI,CAAC,CAAE,CACzE,CACAzL,EAAO8L,GAAmB,mBAAmB,EAC7C,SAASC,GAAgBN,EAAS,CAChCF,EAAgB,QAAQ,CAAE,aAAcH,EAAcK,EAAQ,IAAI,CAAC,CAAE,CACvE,CACAzL,EAAO+L,GAAiB,iBAAiB,EACzC,SAASC,GAAmBP,EAAS,CACnCF,EAAgB,QAAQ,CAAE,gBAAiBH,EAAcK,EAAQ,IAAI,CAAC,CAAE,CAC1E,CACAzL,EAAOgM,GAAoB,oBAAoB,EAC/C,SAASC,GAAYvD,EAAQ,CAC3B,MAAMwD,EAAe,CAAA,EACrB,UAAWtB,KAASlC,EAAQ,CAC1B,KAAM,CAACyD,EAAKtB,CAAK,EAAID,EAAM,OAAO,MAAM,SAAS,EACjD,GAAIuB,IAAQ,SAAU,CACpB,GAAInB,GAAeH,CAAK,EACtB,MAAM,IAAIF,GAAkBwB,EAAKtB,EAAO,QAAQ,EAElDqB,EAAa,OAAS,SAASrB,CAAK,CAC1C,SAAesB,IAAQ,QAAS,CAC1B,GAAIpB,GAAgBF,CAAK,EACvB,MAAM,IAAIF,GAAkBwB,EAAKtB,EAAO,UAAU,EAEpDqB,EAAa,MAAQrB,CAC3B,SAAesB,IAAQ,eAAgB,CACjC,GAAIpB,GAAgBF,CAAK,EACvB,MAAM,IAAIF,GAAkBwB,EAAKtB,EAAO,UAAU,EAEpDqB,EAAa,YAAcrB,CACjC,SAAesB,IAAQ,eAAgB,CACjC,GAAIlB,GAAqBJ,CAAK,EAC5B,MAAM,IAAIF,GAAkBwB,EAAKtB,EAAO,6BAA6B,EAEvEqB,EAAa,YAAcrB,CACjC,KACM,OAAM,IAAI,MAAM,eAAesB,CAAG,oBAAoB,CAEzD,CACD,OAAOD,CACT,CACAlM,EAAOiM,GAAa,aAAa,EACjC,SAASG,GAASX,EAAShD,EAAW4D,EAAGC,EAAG5D,EAAQ,CAClD,MAAMwD,EAAeD,GAAYvD,CAAM,EACvC6C,EAAgB,UAAU,CACxB,CACE,EAAAc,EACA,EAAAC,EACA,KAAMlB,EAAcK,EAAQ,IAAI,EAChC,UAAAhD,EACA,GAAGyD,CACJ,CACL,CAAG,CACH,CACAlM,EAAOoM,GAAU,UAAU,EAC3B,SAASG,GAAS9D,EAAWC,EAAQ,CACnC6C,EAAgB,SAAS9C,EAAWwD,GAAYvD,CAAM,CAAC,CACzD,CACA1I,EAAOuM,GAAU,UAAU,EAC3B,SAASC,GAASC,EAAO,CACvBlB,EAAgB,UAAU,CAAE,WAAYkB,CAAO,CAAA,CACjD,CACAzM,EAAOwM,GAAU,UAAU,EAC3B,SAASE,GAAUC,EAAQ,CACzBpB,EAAgB,UAAU,CAAE,YAAaoB,CAAQ,CAAA,CACnD,CACA3M,EAAO0M,GAAW,WAAW,EAC7B,SAASE,IAAkB,CACzB,MAAMjE,EAAUwC,KACV,CAAE,eAAA0B,EAAgB,cAAeC,CAAmB,EAAKnE,EAC/D,OAAImE,GACFvB,EAAgB,UAAUuB,CAAmB,EAE/CvB,EAAgB,eAAe,CAC7B,cAAesB,EAAe,cAC9B,cAAeA,EAAe,cAC9B,cAAeA,EAAe,cAC9B,cAAeA,EAAe,cAC9B,kBAAmBA,EAAe,kBAClC,kBAAmBA,EAAe,kBAClC,kBAAmBA,EAAe,kBAClC,kBAAmBA,EAAe,kBAClC,kBAAmBA,EAAe,kBAClC,sBAAuBA,EAAe,sBACtC,sBAAuBA,EAAe,sBACtC,sBAAuBA,EAAe,sBACtC,iCAAkCA,EAAe,iCACjD,iCAAkCA,EAAe,iCACjD,kBAAmBA,EAAe,iBACtC,CAAG,EACDtB,EAAgB,QAAQ,CAAE,UAAWwB,GAAe,CAAI,CAAA,EACjDxB,EAAgB,OACzB,CACAvL,EAAO4M,GAAiB,iBAAiB,EACzC,IAAII,GAAyBhN,EAAO,UAAW,CAC7CuL,EAAgB,MAAK,EACrB0B,IACF,EAAG,OAAO,EACNC,GAAqB,CACvB,SAAAV,GACA,UAAAE,GACA,iBAAAlB,GACA,iBAAAE,GACA,iBAAAC,GACA,iBAAAC,GACA,iBAAAC,GACA,kBAAAC,GACA,gBAAAC,GACA,mBAAAC,GACA,YAAAC,GACA,SAAAG,GACA,SAAAG,GACA,gBAAAK,GACA,MAAOI,GACP,YAAAG,GACA,YAAAC,GACA,gBAAAC,GACA,gBAAAN,GACA,kBAAAO,GACA,kBAAAC,EACF,EAIIC,GAAuBxN,EAAO,CAACyN,EAAKC,EAAIC,EAAUC,IAAY,CAChE,SAASC,EAAoBC,EAAe,CAC1C,OAAOA,IAAkB,MAAQ,UAAY,QAC9C,CACD9N,EAAO6N,EAAqB,qBAAqB,EACjD,SAASE,EAAcC,EAAa,CAClC,OAAOA,IAAgB,OAAS,QAAU,QAC3C,CACDhO,EAAO+N,EAAe,eAAe,EACrC,SAASE,EAAkB1F,EAAM,CAC/B,MAAO,aAAaA,EAAK,CAAC,KAAKA,EAAK,CAAC,YAAYA,EAAK,UAAY,CAAC,GACpE,CACDvI,EAAOiO,EAAmB,mBAAmB,EAC7C,MAAMC,EAAO/C,KACb7C,GAAI,MAAM;AAAA,EAA+BmF,CAAG,EAC5C,MAAMU,EAAgBD,EAAK,cAC3B,IAAIE,EACAD,IAAkB,YACpBC,EAAiBC,GAAO,KAAOX,CAAE,GAGnC,MAAMY,GADOH,IAAkB,UAAYE,GAAOD,EAAe,MAAK,EAAG,CAAC,EAAE,gBAAgB,IAAI,EAAIC,GAAO,MAAM,GAChG,OAAO,QAAQX,CAAE,IAAI,EAChCa,EAAQD,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,MAAM,EAC5C7B,EAAQyB,EAAK,eAAe,YAAc,IAC1CvB,EAASuB,EAAK,eAAe,aAAe,IAClDM,GAAiBF,EAAK3B,EAAQF,EAAOyB,EAAK,eAAe,aAAe,EAAI,EAC5EI,EAAI,KAAK,UAAW,OAAS7B,EAAQ,IAAME,CAAM,EACjDiB,EAAQ,GAAG,UAAUjB,CAAM,EAC3BiB,EAAQ,GAAG,SAASnB,CAAK,EACzB,MAAMgC,EAAeb,EAAQ,GAAG,gBAAe,EACzCc,GAAiBH,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,WAAW,EAC5DI,GAAcJ,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,QAAQ,EACtDK,GAAiBL,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EAC9DM,GAAaN,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,QAAQ,EACrDO,GAAaP,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EACtDE,EAAa,OACfK,GAAW,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,OAAQL,EAAa,MAAM,IAAI,EAAE,KAAK,YAAaA,EAAa,MAAM,QAAQ,EAAE,KAAK,oBAAqBZ,EAAoBY,EAAa,MAAM,aAAa,CAAC,EAAE,KAAK,cAAeV,EAAcU,EAAa,MAAM,WAAW,CAAC,EAAE,KAAK,YAAaR,EAAkBQ,EAAa,KAAK,CAAC,EAAE,KAAKA,EAAa,MAAM,IAAI,EAEhXA,EAAa,aACfE,GAAY,UAAU,MAAM,EAAE,KAAKF,EAAa,WAAW,EAAE,MAAK,EAAG,OAAO,MAAM,EAAE,KAAK,KAAOlG,GAASA,EAAK,EAAE,EAAE,KAAK,KAAOA,GAASA,EAAK,EAAE,EAAE,KAAK,KAAOA,GAASA,EAAK,EAAE,EAAE,KAAK,KAAOA,GAASA,EAAK,EAAE,EAAE,MAAM,SAAWA,GAASA,EAAK,UAAU,EAAE,MAAM,eAAiBA,GAASA,EAAK,WAAW,EAEzS,MAAM2B,GAAYwE,GAAe,UAAU,YAAY,EAAE,KAAKD,EAAa,SAAS,EAAE,MAAK,EAAG,OAAO,GAAG,EAAE,KAAK,QAAS,UAAU,EAClIvE,GAAU,OAAO,MAAM,EAAE,KAAK,IAAM3B,GAASA,EAAK,CAAC,EAAE,KAAK,IAAMA,GAASA,EAAK,CAAC,EAAE,KAAK,QAAUA,GAASA,EAAK,KAAK,EAAE,KAAK,SAAWA,GAASA,EAAK,MAAM,EAAE,KAAK,OAASA,GAASA,EAAK,IAAI,EAC3L2B,GAAU,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,OAAS3B,GAASA,EAAK,KAAK,IAAI,EAAE,KAAK,YAAcA,GAASA,EAAK,KAAK,QAAQ,EAAE,KACxI,oBACCA,GAASsF,EAAoBtF,EAAK,KAAK,aAAa,CACtD,EAAC,KAAK,cAAgBA,GAASwF,EAAcxF,EAAK,KAAK,WAAW,CAAC,EAAE,KAAK,YAAcA,GAAS0F,EAAkB1F,EAAK,IAAI,CAAC,EAAE,KAAMA,GAASA,EAAK,KAAK,IAAI,EAC9IsG,GAAW,UAAU,SAAS,EAAE,KAAKJ,EAAa,UAAU,EAAE,MAAK,EAAG,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAC/G,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAK,CAAC,EAAE,KAAMlG,GAASA,EAAK,IAAI,EAAE,KAAK,OAASA,GAASA,EAAK,IAAI,EAAE,KAAK,YAAcA,GAASA,EAAK,QAAQ,EAAE,KAAK,oBAAsBA,GAASsF,EAAoBtF,EAAK,aAAa,CAAC,EAAE,KAAK,cAAgBA,GAASwF,EAAcxF,EAAK,WAAW,CAAC,EAAE,KAAK,YAAcA,GAAS0F,EAAkB1F,CAAI,CAAC,EACtV,MAAMwG,GAAaH,GAAe,UAAU,cAAc,EAAE,KAAKH,EAAa,MAAM,EAAE,MAAK,EAAG,OAAO,GAAG,EAAE,KAAK,QAAS,YAAY,EACpIM,GAAW,OAAO,QAAQ,EAAE,KAAK,KAAOxG,GAASA,EAAK,CAAC,EAAE,KAAK,KAAOA,GAASA,EAAK,CAAC,EAAE,KAAK,IAAMA,GAASA,EAAK,MAAM,EAAE,KAAK,OAASA,GAASA,EAAK,IAAI,EAAE,KAAK,SAAWA,GAASA,EAAK,WAAW,EAAE,KAAK,eAAiBA,GAASA,EAAK,WAAW,EACnPwG,GAAW,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAK,CAAC,EAAE,KAAMxG,GAASA,EAAK,KAAK,IAAI,EAAE,KAAK,OAASA,GAASA,EAAK,KAAK,IAAI,EAAE,KAAK,YAAcA,GAASA,EAAK,KAAK,QAAQ,EAAE,KACxK,oBACCA,GAASsF,EAAoBtF,EAAK,KAAK,aAAa,CACzD,EAAI,KAAK,cAAgBA,GAASwF,EAAcxF,EAAK,KAAK,WAAW,CAAC,EAAE,KAAK,YAAcA,GAAS0F,EAAkB1F,EAAK,IAAI,CAAC,CAChI,EAAG,MAAM,EACLyG,GAA2B,CAC7B,KAAAxB,EACF,EAGIyB,GAAU,CACZ,OAAQhH,GACR,GAAIiF,GACJ,SAAU8B,GACV,OAAwBhP,EAAO,IAAM,GAAI,QAAQ,CACnD", "x_google_ignoreList": [0]}