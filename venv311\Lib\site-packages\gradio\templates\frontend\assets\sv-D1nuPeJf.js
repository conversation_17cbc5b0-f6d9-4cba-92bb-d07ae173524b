const e="Svenska",t={annotated_image:"Annoterad bild"},r={allow_recording_access:"Tillåt mikrofonåtkomst för inspelning.",audio:"Ljud",record_from_microphone:"Spela in från mikrofon",stop_recording:"Stoppa inspelning",no_device_support:"Kan inte få åtkomst till medieenheter. Se till att du använder en säker källa (https) eller localhost (eller har gett ett giltigt SSL-certifikat till ssl_verify) och att du har gett webbläsaren åtkomst till din enhet.",stop:"Stopp",resume:"Fortsätt",record:"Spela in",no_microphone:"Ingen mikrofon hittades",pause:"Paus",play:"Spela upp",waiting:"Väntar",drop_to_upload:"Släpp en ljudfil här för att ladda upp"},a={connection_can_break:"På mobila enheter kan anslutningen brytas om fliken lämnas eller om enheten går i viloläge, och du förlorar din plats i kön.",long_requests_queue:"Det är en lång kö med väntande förfrågningar. Duplicera detta Space för att hoppa över kön.",lost_connection:"Anslutningen bröts eftersom sidan lämnades. Återgår till kön...",waiting_for_inputs:"Väntar på att fil(er) ska vara klara att ladda upp, vänligen försök igen."},o={checkbox:"Kryssruta",checkbox_group:"Kryssrutgrupp"},n={code:"Kod"},l={color_picker:"Färgväljare"},i={built_with:"Byggt med",built_with_gradio:"Byggt med Gradio",clear:"Rensa",download:"Ladda ner",edit:"Redigera",empty:"Tom",error:"Fel",hosted_on:"Värd på",loading:"Laddar",logo:"Logotyp",or:"eller",remove:"Ta bort",settings:"Inställningar",share:"Dela",submit:"Skicka",undo:"Ångra",no_devices:"Inga enheter hittades",language:"Språk",display_theme:"Visningstema",pwa:"Progressiv webbapplikation"},d={incorrect_format:"Fel format, endast CSV- och TSV-filer stöds",new_column:"Lägg till kolumn",new_row:"Ny rad",add_row_above:"Lägg till rad ovanför",add_row_below:"Lägg till rad nedanför",add_column_left:"Lägg till kolumn till vänster",add_column_right:"Lägg till kolumn till höger",delete_row:"Ta bort rad",delete_column:"Ta bort kolumn",sort_column:"Sortera kolumn",sort_ascending:"Sortera stigande",sort_descending:"Sortera fallande",drop_to_upload:"Släpp CSV- eller TSV-filer här för att importera data till dataramverk",clear_sort:"Rensa sortering"},s={dropdown:"Rullgardinsmeny"},p={build_error:"Det finns ett byggfel",config_error:"Det finns ett konfigurationsfel",contact_page_author:"Kontakta sidans författare för att informera dem.",no_app_file:"Det finns ingen app-fil",runtime_error:"Det finns ett körtidsfel",space_not_working:'"Space fungerar inte eftersom" {0}',space_paused:"Space är pausat",use_via_api:"Använd via API",use_via_api_or_mcp:"Använd via API eller MCP"},c={uploading:"Laddar upp..."},g={highlighted_text:"Markerad text"},_={allow_webcam_access:"Tillåt webbkameråtkomst för inspelning.",brush_color:"Penselfärg",brush_radius:"Penselstorlek",image:"Bild",remove_image:"Ta bort bild",select_brush_color:"Välj penselfärg",start_drawing:"Börja rita",use_brush:"Använd pensel",drop_to_upload:"Släpp en bildfil här för att ladda upp"},u={label:"Etikett"},m={enable_cookies:"Om du besöker ett HuggingFace Space i inkognitoläge måste du aktivera tredjepartscookies.",incorrect_credentials:"Felaktiga inloggningsuppgifter",username:"Användarnamn",password:"Lösenord",login:"Logga in"},f={number:"Nummer"},k={plot:"Diagram"},h={radio:"Radioknapp"},b={slider:"Skjutreglage"},S={click_to_upload:"Klicka för att ladda upp",drop_audio:"Släpp ljud här",drop_csv:"Släpp CSV här",drop_file:"Släpp fil här",drop_image:"Släpp bild här",drop_video:"Släpp video här",drop_gallery:"Släpp media här",paste_clipboard:"Klistra in från urklipp"},v={drop_to_upload:"Släpp en videofil här för att ladda upp"},w={edit:"Redigera",retry:"Försök igen",undo:"Ångra",submit:"Skicka",cancel:"Avbryt",like:"Gilla",dislike:"Ogilla",clear:"Rensa chatten"},y={_name:e,"3D_model":{"3d_model":"3D-modell",drop_to_upload:"Släpp en 3D-modellfil (.obj, .glb, .stl, .gltf, .splat eller .ply) här för att ladda upp"},annotated_image:t,audio:r,blocks:a,checkbox:o,code:n,color_picker:l,common:i,dataframe:d,dropdown:s,errors:p,file:c,highlighted_text:g,image:_,label:u,login:m,number:f,plot:k,radio:h,slider:b,upload_text:S,video:v,chatbot:w};export{e as _name,t as annotated_image,r as audio,a as blocks,w as chatbot,o as checkbox,n as code,l as color_picker,i as common,d as dataframe,y as default,s as dropdown,p as errors,c as file,g as highlighted_text,_ as image,u as label,m as login,f as number,k as plot,h as radio,b as slider,S as upload_text,v as video};
//# sourceMappingURL=sv-D1nuPeJf.js.map
