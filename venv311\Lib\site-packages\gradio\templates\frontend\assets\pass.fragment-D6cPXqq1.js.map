{"version": 3, "file": "pass.fragment-D6cPXqq1.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/pass.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"passPixelShader\";\nconst shader = `varying vec2 vUV;uniform sampler2D textureSampler;\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) \n{gl_FragColor=texture2D(textureSampler,vUV);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const passPixelShader = { name, shader };\n//# sourceMappingURL=pass.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "passPixelShader"], "mappings": "+FAEA,MAAMA,EAAO,kBACPC,EAAS;AAAA;AAAA;AAAA,+CAKVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAkB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}