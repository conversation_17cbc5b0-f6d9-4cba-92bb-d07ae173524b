{"version": 3, "file": "index-B1FJGuzG.js", "sources": ["../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/environment.js", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/loop.js", "../../../../js/statustracker/static/utils.ts", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/motion/utils.js", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/motion/spring.js", "../../../../js/statustracker/static/Loader.svelte", "../../../../js/statustracker/static/index.svelte"], "sourcesContent": ["import { noop } from './utils.js';\n\nexport const is_client = typeof window !== 'undefined';\n\n/** @type {() => number} */\nexport let now = is_client ? () => window.performance.now() : () => Date.now();\n\nexport let raf = is_client ? (cb) => requestAnimationFrame(cb) : noop;\n\n// used internally for testing\n/** @returns {void} */\nexport function set_now(fn) {\n\tnow = fn;\n}\n\n/** @returns {void} */\nexport function set_raf(fn) {\n\traf = fn;\n}\n", "import { raf } from './environment.js';\n\nconst tasks = new Set();\n\n/**\n * @param {number} now\n * @returns {void}\n */\nfunction run_tasks(now) {\n\ttasks.forEach((task) => {\n\t\tif (!task.c(now)) {\n\t\t\ttasks.delete(task);\n\t\t\ttask.f();\n\t\t}\n\t});\n\tif (tasks.size !== 0) raf(run_tasks);\n}\n\n/**\n * For testing purposes only!\n * @returns {void}\n */\nexport function clear_loops() {\n\ttasks.clear();\n}\n\n/**\n * Creates a new task that runs on each raf frame\n * until it returns a falsy value or is aborted\n * @param {import('./private.js').TaskCallback} callback\n * @returns {import('./private.js').Task}\n */\nexport function loop(callback) {\n\t/** @type {import('./private.js').TaskEntry} */\n\tlet task;\n\tif (tasks.size === 0) raf(run_tasks);\n\treturn {\n\t\tpromise: new Promise((fulfill) => {\n\t\t\ttasks.add((task = { c: callback, f: fulfill }));\n\t\t}),\n\t\tabort() {\n\t\t\ttasks.delete(task);\n\t\t}\n\t};\n}\n", "export function pretty_si(num: number): string {\n\tlet units = [\"\", \"k\", \"M\", \"G\", \"T\", \"P\", \"E\", \"Z\"];\n\tlet i = 0;\n\twhile (num > 1000 && i < units.length - 1) {\n\t\tnum /= 1000;\n\t\ti++;\n\t}\n\tlet unit = units[i];\n\treturn (Number.isInteger(num) ? num : num.toFixed(1)) + unit;\n}\n", "/**\n * @param {any} obj\n * @returns {boolean}\n */\nexport function is_date(obj) {\n\treturn Object.prototype.toString.call(obj) === '[object Date]';\n}\n", "import { writable } from '../store/index.js';\nimport { loop, now } from '../internal/index.js';\nimport { is_date } from './utils.js';\n\n/**\n * @template T\n * @param {import('./private.js').TickContext<T>} ctx\n * @param {T} last_value\n * @param {T} current_value\n * @param {T} target_value\n * @returns {T}\n */\nfunction tick_spring(ctx, last_value, current_value, target_value) {\n\tif (typeof current_value === 'number' || is_date(current_value)) {\n\t\t// @ts-ignore\n\t\tconst delta = target_value - current_value;\n\t\t// @ts-ignore\n\t\tconst velocity = (current_value - last_value) / (ctx.dt || 1 / 60); // guard div by 0\n\t\tconst spring = ctx.opts.stiffness * delta;\n\t\tconst damper = ctx.opts.damping * velocity;\n\t\tconst acceleration = (spring - damper) * ctx.inv_mass;\n\t\tconst d = (velocity + acceleration) * ctx.dt;\n\t\tif (Math.abs(d) < ctx.opts.precision && Math.abs(delta) < ctx.opts.precision) {\n\t\t\treturn target_value; // settled\n\t\t} else {\n\t\t\tctx.settled = false; // signal loop to keep ticking\n\t\t\t// @ts-ignore\n\t\t\treturn is_date(current_value) ? new Date(current_value.getTime() + d) : current_value + d;\n\t\t}\n\t} else if (Array.isArray(current_value)) {\n\t\t// @ts-ignore\n\t\treturn current_value.map((_, i) =>\n\t\t\ttick_spring(ctx, last_value[i], current_value[i], target_value[i])\n\t\t);\n\t} else if (typeof current_value === 'object') {\n\t\tconst next_value = {};\n\t\tfor (const k in current_value) {\n\t\t\t// @ts-ignore\n\t\t\tnext_value[k] = tick_spring(ctx, last_value[k], current_value[k], target_value[k]);\n\t\t}\n\t\t// @ts-ignore\n\t\treturn next_value;\n\t} else {\n\t\tthrow new Error(`Cannot spring ${typeof current_value} values`);\n\t}\n}\n\n/**\n * The spring function in Svelte creates a store whose value is animated, with a motion that simulates the behavior of a spring. This means when the value changes, instead of transitioning at a steady rate, it \"bounces\" like a spring would, depending on the physics parameters provided. This adds a level of realism to the transitions and can enhance the user experience.\n *\n * https://svelte.dev/docs/svelte-motion#spring\n * @template [T=any]\n * @param {T} [value]\n * @param {import('./private.js').SpringOpts} [opts]\n * @returns {import('./public.js').Spring<T>}\n */\nexport function spring(value, opts = {}) {\n\tconst store = writable(value);\n\tconst { stiffness = 0.15, damping = 0.8, precision = 0.01 } = opts;\n\t/** @type {number} */\n\tlet last_time;\n\t/** @type {import('../internal/private.js').Task} */\n\tlet task;\n\t/** @type {object} */\n\tlet current_token;\n\t/** @type {T} */\n\tlet last_value = value;\n\t/** @type {T} */\n\tlet target_value = value;\n\tlet inv_mass = 1;\n\tlet inv_mass_recovery_rate = 0;\n\tlet cancel_task = false;\n\t/**\n\t * @param {T} new_value\n\t * @param {import('./private.js').SpringUpdateOpts} opts\n\t * @returns {Promise<void>}\n\t */\n\tfunction set(new_value, opts = {}) {\n\t\ttarget_value = new_value;\n\t\tconst token = (current_token = {});\n\t\tif (value == null || opts.hard || (spring.stiffness >= 1 && spring.damping >= 1)) {\n\t\t\tcancel_task = true; // cancel any running animation\n\t\t\tlast_time = now();\n\t\t\tlast_value = new_value;\n\t\t\tstore.set((value = target_value));\n\t\t\treturn Promise.resolve();\n\t\t} else if (opts.soft) {\n\t\t\tconst rate = opts.soft === true ? 0.5 : +opts.soft;\n\t\t\tinv_mass_recovery_rate = 1 / (rate * 60);\n\t\t\tinv_mass = 0; // infinite mass, unaffected by spring forces\n\t\t}\n\t\tif (!task) {\n\t\t\tlast_time = now();\n\t\t\tcancel_task = false;\n\t\t\ttask = loop((now) => {\n\t\t\t\tif (cancel_task) {\n\t\t\t\t\tcancel_task = false;\n\t\t\t\t\ttask = null;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tinv_mass = Math.min(inv_mass + inv_mass_recovery_rate, 1);\n\t\t\t\tconst ctx = {\n\t\t\t\t\tinv_mass,\n\t\t\t\t\topts: spring,\n\t\t\t\t\tsettled: true,\n\t\t\t\t\tdt: ((now - last_time) * 60) / 1000\n\t\t\t\t};\n\t\t\t\tconst next_value = tick_spring(ctx, last_value, value, target_value);\n\t\t\t\tlast_time = now;\n\t\t\t\tlast_value = value;\n\t\t\t\tstore.set((value = next_value));\n\t\t\t\tif (ctx.settled) {\n\t\t\t\t\ttask = null;\n\t\t\t\t}\n\t\t\t\treturn !ctx.settled;\n\t\t\t});\n\t\t}\n\t\treturn new Promise((fulfil) => {\n\t\t\ttask.promise.then(() => {\n\t\t\t\tif (token === current_token) fulfil();\n\t\t\t});\n\t\t});\n\t}\n\t/** @type {import('./public.js').Spring<T>} */\n\tconst spring = {\n\t\tset,\n\t\tupdate: (fn, opts) => set(fn(target_value, value), opts),\n\t\tsubscribe: store.subscribe,\n\t\tstiffness,\n\t\tdamping,\n\t\tprecision\n\t};\n\treturn spring;\n}\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { spring } from \"svelte/motion\";\n\n\texport let margin = true;\n\n\tconst top = spring([0, 0]);\n\tconst bottom = spring([0, 0]);\n\n\tlet dismounted: boolean;\n\n\tasync function animate(): Promise<void> {\n\t\tawait Promise.all([top.set([125, 140]), bottom.set([-125, -140])]);\n\t\tawait Promise.all([top.set([-125, 140]), bottom.set([125, -140])]);\n\t\tawait Promise.all([top.set([-125, 0]), bottom.set([125, -0])]);\n\t\tawait Promise.all([top.set([125, 0]), bottom.set([-125, 0])]);\n\t}\n\n\tasync function run(): Promise<void> {\n\t\tawait animate();\n\t\tif (!dismounted) run();\n\t}\n\n\tasync function loading(): Promise<void> {\n\t\tawait Promise.all([top.set([125, 0]), bottom.set([-125, 0])]);\n\n\t\trun();\n\t}\n\n\tonMount(() => {\n\t\tloading();\n\t\treturn (): boolean => (dismounted = true);\n\t});\n</script>\n\n<div class:margin>\n\t<svg\n\t\tviewBox=\"-1200 -1200 3000 3000\"\n\t\tfill=\"none\"\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t>\n\t\t<g style=\"transform: translate({$top[0]}px, {$top[1]}px);\">\n\t\t\t<path\n\t\t\t\td=\"M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t</g>\n\t\t<g style=\"transform: translate({$bottom[0]}px, {$bottom[1]}px);\">\n\t\t\t<path\n\t\t\t\td=\"M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t</g>\n\t</svg>\n</div>\n\n<style>\n\tsvg {\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t}\n\n\tsvg path {\n\t\tfill: var(--loader-color);\n\t}\n\n\tdiv {\n\t\tz-index: var(--layer-2);\n\t}\n\n\t.margin {\n\t\tmargin: var(--size-4);\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\timport { tick } from \"svelte\";\n\timport { pretty_si } from \"./utils\";\n\n\tlet items: HTMLDivElement[] = [];\n\n\tlet called = false;\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tconst raf = is_browser\n\t\t? window.requestAnimationFrame\n\t\t: (cb: (...args: any[]) => void) => {};\n\n\tasync function scroll_into_view(\n\t\tel: HTMLDivElement,\n\t\tenable: boolean | null = true\n\t): Promise<void> {\n\t\tif (\n\t\t\twindow.__gradio_mode__ === \"website\" ||\n\t\t\t(window.__gradio_mode__ !== \"app\" && enable !== true)\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\titems.push(el);\n\t\tif (!called) called = true;\n\t\telse return;\n\n\t\tawait tick();\n\n\t\traf(() => {\n\t\t\tlet min = [0, 0];\n\n\t\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\t\tconst element = items[i];\n\n\t\t\t\tconst box = element.getBoundingClientRect();\n\t\t\t\tif (i === 0 || box.top + window.scrollY <= min[0]) {\n\t\t\t\t\tmin[0] = box.top + window.scrollY;\n\t\t\t\t\tmin[1] = i;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twindow.scrollTo({ top: min[0] - 20, behavior: \"smooth\" });\n\n\t\t\tcalled = false;\n\t\t\titems = [];\n\t\t});\n\t}\n</script>\n\n<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\n\timport Loader from \"./Loader.svelte\";\n\timport type { LoadingStatus } from \"./types\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Clear } from \"@gradio/icons\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let i18n: I18nFormatter;\n\texport let eta: number | null = null;\n\texport let queue_position: number | null;\n\texport let queue_size: number | null;\n\texport let status:\n\t\t| \"complete\"\n\t\t| \"pending\"\n\t\t| \"error\"\n\t\t| \"generating\"\n\t\t| \"streaming\"\n\t\t| null;\n\texport let scroll_to_output = false;\n\texport let timer = true;\n\texport let show_progress: \"full\" | \"minimal\" | \"hidden\" = \"full\";\n\texport let message: string | null = null;\n\texport let progress: LoadingStatus[\"progress\"] | null | undefined = null;\n\texport let variant: \"default\" | \"center\" = \"default\";\n\texport let loading_text = \"Loading...\";\n\texport let absolute = true;\n\texport let translucent = false;\n\texport let border = false;\n\texport let autoscroll: boolean;\n\n\tlet el: HTMLDivElement;\n\n\tlet _timer = false;\n\tlet timer_start = 0;\n\tlet timer_diff = 0;\n\tlet old_eta: number | null = null;\n\tlet eta_from_start: number | null = null;\n\tlet message_visible = false;\n\tlet eta_level: number | null = 0;\n\tlet progress_level: (number | undefined)[] | null = null;\n\tlet last_progress_level: number | undefined = undefined;\n\tlet progress_bar: HTMLElement | null = null;\n\tlet show_eta_bar = true;\n\n\t$: eta_level =\n\t\teta_from_start === null || eta_from_start <= 0 || !timer_diff\n\t\t\t? null\n\t\t\t: Math.min(timer_diff / eta_from_start, 1);\n\t$: if (progress != null) {\n\t\tshow_eta_bar = false;\n\t}\n\n\t$: {\n\t\tif (progress != null) {\n\t\t\tprogress_level = progress.map((p) => {\n\t\t\t\tif (p.index != null && p.length != null) {\n\t\t\t\t\treturn p.index / p.length;\n\t\t\t\t} else if (p.progress != null) {\n\t\t\t\t\treturn p.progress;\n\t\t\t\t}\n\t\t\t\treturn undefined;\n\t\t\t});\n\t\t} else {\n\t\t\tprogress_level = null;\n\t\t}\n\n\t\tif (progress_level) {\n\t\t\tlast_progress_level = progress_level[progress_level.length - 1];\n\t\t\tif (progress_bar) {\n\t\t\t\tif (last_progress_level === 0) {\n\t\t\t\t\tprogress_bar.style.transition = \"0\";\n\t\t\t\t} else {\n\t\t\t\t\tprogress_bar.style.transition = \"150ms\";\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tlast_progress_level = undefined;\n\t\t}\n\t}\n\n\tconst start_timer = (): void => {\n\t\teta = old_eta = formatted_eta = null;\n\t\ttimer_start = performance.now();\n\t\ttimer_diff = 0;\n\t\t_timer = true;\n\t\trun();\n\t};\n\n\tfunction run(): void {\n\t\traf(() => {\n\t\t\ttimer_diff = (performance.now() - timer_start) / 1000;\n\t\t\tif (_timer) run();\n\t\t});\n\t}\n\n\tfunction stop_timer(): void {\n\t\ttimer_diff = 0;\n\t\teta = old_eta = formatted_eta = null;\n\n\t\tif (!_timer) return;\n\t\t_timer = false;\n\t}\n\n\tonDestroy(() => {\n\t\tif (_timer) stop_timer();\n\t});\n\n\t$: {\n\t\tif (status === \"pending\") {\n\t\t\tstart_timer();\n\t\t} else {\n\t\t\tstop_timer();\n\t\t}\n\t}\n\n\t$: el &&\n\t\tscroll_to_output &&\n\t\t(status === \"pending\" || status === \"complete\") &&\n\t\tscroll_into_view(el, autoscroll);\n\n\tlet formatted_eta: string | null = null;\n\t$: {\n\t\tif (eta === null) {\n\t\t\teta = old_eta;\n\t\t}\n\t\tif (eta != null && old_eta !== eta) {\n\t\t\teta_from_start = (performance.now() - timer_start) / 1000 + eta;\n\t\t\tformatted_eta = eta_from_start.toFixed(1);\n\t\t\told_eta = eta;\n\t\t}\n\t}\n\tlet show_message_timeout: NodeJS.Timeout | null = null;\n\tfunction close_message(): void {\n\t\tmessage_visible = false;\n\t\tif (show_message_timeout !== null) {\n\t\t\tclearTimeout(show_message_timeout);\n\t\t}\n\t}\n\t$: {\n\t\tclose_message();\n\t\tif (status === \"error\" && message) {\n\t\t\tmessage_visible = true;\n\t\t}\n\t}\n\t$: formatted_timer = timer_diff.toFixed(1);\n</script>\n\n<div\n\tclass=\"wrap {variant} {show_progress}\"\n\tclass:hide={!status ||\n\t\tstatus === \"complete\" ||\n\t\tshow_progress === \"hidden\" ||\n\t\tstatus == \"streaming\"}\n\tclass:translucent={(variant === \"center\" &&\n\t\t(status === \"pending\" || status === \"error\")) ||\n\t\ttranslucent ||\n\t\tshow_progress === \"minimal\"}\n\tclass:generating={status === \"generating\" && show_progress === \"full\"}\n\tclass:border\n\tstyle:position={absolute ? \"absolute\" : \"static\"}\n\tstyle:padding={absolute ? \"0\" : \"var(--size-8) 0\"}\n\tbind:this={el}\n>\n\t{#if status === \"pending\"}\n\t\t{#if variant === \"default\" && show_eta_bar && show_progress === \"full\"}\n\t\t\t<div\n\t\t\t\tclass=\"eta-bar\"\n\t\t\t\tstyle:transform=\"translateX({(eta_level || 0) * 100 - 100}%)\"\n\t\t\t/>\n\t\t{/if}\n\t\t<div\n\t\t\tclass:meta-text-center={variant === \"center\"}\n\t\t\tclass:meta-text={variant === \"default\"}\n\t\t\tclass=\"progress-text\"\n\t\t>\n\t\t\t{#if progress}\n\t\t\t\t{#each progress as p}\n\t\t\t\t\t{#if p.index != null}\n\t\t\t\t\t\t{#if p.length != null}\n\t\t\t\t\t\t\t{pretty_si(p.index || 0)}/{pretty_si(p.length)}\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{pretty_si(p.index || 0)}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{p.unit} | {\" \"}\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{:else if queue_position !== null && queue_size !== undefined && queue_position >= 0}\n\t\t\t\tqueue: {queue_position + 1}/{queue_size} |\n\t\t\t{:else if queue_position === 0}\n\t\t\t\tprocessing |\n\t\t\t{/if}\n\n\t\t\t{#if timer}\n\t\t\t\t{formatted_timer}{eta ? `/${formatted_eta}` : \"\"}s\n\t\t\t{/if}\n\t\t</div>\n\n\t\t{#if last_progress_level != null}\n\t\t\t<div class=\"progress-level\">\n\t\t\t\t<div class=\"progress-level-inner\">\n\t\t\t\t\t{#if progress != null}\n\t\t\t\t\t\t{#each progress as p, i}\n\t\t\t\t\t\t\t{#if p.desc != null || (progress_level && progress_level[i] != null)}\n\t\t\t\t\t\t\t\t{#if i !== 0}\n\t\t\t\t\t\t\t\t\t&nbsp;/\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if p.desc != null}\n\t\t\t\t\t\t\t\t\t{p.desc}\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if p.desc != null && progress_level && progress_level[i] != null}\n\t\t\t\t\t\t\t\t\t-\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if progress_level != null}\n\t\t\t\t\t\t\t\t\t{(100 * (progress_level[i] || 0)).toFixed(1)}%\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\n\t\t\t\t<div class=\"progress-bar-wrap\">\n\t\t\t\t\t<div\n\t\t\t\t\t\tbind:this={progress_bar}\n\t\t\t\t\t\tclass=\"progress-bar\"\n\t\t\t\t\t\tstyle:width=\"{last_progress_level * 100}%\"\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t{:else if show_progress === \"full\"}\n\t\t\t<Loader margin={variant === \"default\"} />\n\t\t{/if}\n\n\t\t{#if !timer}\n\t\t\t<p class=\"loading\">{loading_text}</p>\n\t\t\t<slot name=\"additional-loading-text\" />\n\t\t{/if}\n\t{:else if status === \"error\"}\n\t\t<div class=\"clear-status\">\n\t\t\t<IconButton\n\t\t\t\tIcon={Clear}\n\t\t\t\tlabel={i18n(\"common.clear\")}\n\t\t\t\tdisabled={false}\n\t\t\t\ton:click={() => {\n\t\t\t\t\tdispatch(\"clear_status\");\n\t\t\t\t}}\n\t\t\t/>\n\t\t</div>\n\t\t<span class=\"error\">{i18n(\"common.error\")}</span>\n\t\t<slot name=\"error\" />\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: var(--layer-2);\n\t\ttransition: opacity 0.1s ease-in-out;\n\t\tborder-radius: var(--block-radius);\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: 0 var(--size-6);\n\t\tmax-height: var(--size-screen-h);\n\t\toverflow: hidden;\n\t}\n\n\t.wrap.center {\n\t\ttop: 0;\n\t\tright: 0px;\n\t\tleft: 0px;\n\t}\n\n\t.wrap.default {\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tbottom: 0px;\n\t\tleft: 0px;\n\t}\n\n\t.hide {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n\n\t.generating {\n\t\tanimation:\n\t\t\tpulseStart 1s cubic-bezier(0.4, 0, 0.6, 1),\n\t\t\tpulse 2s cubic-bezier(0.4, 0, 0.6, 1) 1s infinite;\n\t\tborder: 2px solid var(--color-accent);\n\t\tbackground: transparent;\n\t\tz-index: var(--layer-1);\n\t\tpointer-events: none;\n\t}\n\n\t.translucent {\n\t\tbackground: none;\n\t}\n\n\t@keyframes pulseStart {\n\t\t0% {\n\t\t\topacity: 0;\n\t\t}\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t@keyframes pulse {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t}\n\n\t.loading {\n\t\tz-index: var(--layer-2);\n\t\tcolor: var(--body-text-color);\n\t}\n\t.eta-bar {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\ttransform-origin: left;\n\t\topacity: 0.8;\n\t\tz-index: var(--layer-1);\n\t\ttransition: 10ms;\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\t.progress-bar-wrap {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: 55.5%;\n\t\theight: var(--size-4);\n\t}\n\t.progress-bar {\n\t\ttransform-origin: left;\n\t\tbackground-color: var(--loader-color);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.progress-level {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tgap: 1;\n\t\tz-index: var(--layer-2);\n\t\twidth: var(--size-full);\n\t}\n\n\t.progress-level-inner {\n\t\tmargin: var(--size-2) auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.meta-text {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-2);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.meta-text-center {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransform: translateY(var(--size-6));\n\t\tz-index: var(--layer-2);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t\ttext-align: center;\n\t}\n\n\t.error {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: solid 1px var(--error-border-color);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--error-background-fill);\n\t\tpadding-right: var(--size-4);\n\t\tpadding-left: var(--size-4);\n\t\tcolor: var(--error-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tfont-size: var(--text-lg);\n\t\tline-height: var(--line-lg);\n\t\tfont-family: var(--font);\n\t}\n\n\t.minimal {\n\t\tpointer-events: none;\n\t}\n\n\t.minimal .progress-text {\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.border {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.clear-status {\n\t\tposition: absolute;\n\t\tdisplay: flex;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-1);\n\t}\n</style>\n"], "names": ["is_client", "now", "raf", "cb", "noop", "tasks", "run_tasks", "task", "loop", "callback", "fulfill", "pretty_si", "num", "units", "i", "unit", "is_date", "obj", "tick_spring", "ctx", "last_value", "current_value", "target_value", "delta", "velocity", "spring", "damper", "acceleration", "d", "_", "next_value", "k", "value", "opts", "store", "writable", "stiffness", "damping", "precision", "last_time", "current_token", "inv_mass", "inv_mass_recovery_rate", "cancel_task", "set", "new_value", "token", "fulfil", "fn", "set_style", "g0", "g1", "insert", "target", "div", "anchor", "append", "svg", "path0", "path1", "path2", "path3", "path4", "path5", "path6", "path7", "margin", "$$props", "top", "bottom", "dismounted", "animate", "run", "loading", "onMount", "t1_value", "Clear", "span", "dirty", "iconbutton_changes", "current", "set_data", "t1", "if_block0", "create_if_block_16", "create_if_block_11", "create_if_block_14", "create_if_block_15", "create_if_block_10", "create_if_block_1", "toggle_class", "style_transform", "create_if_block_13", "t_value", "t", "t0_value", "t0", "t2", "t2_value", "create_if_block_12", "loader_changes", "style_width", "if_block", "create_if_block_3", "div3", "div0", "div2", "div1", "create_if_block_8", "create_if_block_7", "create_if_block_6", "if_block3", "create_if_block_5", "create_if_block_4", "p_1", "attr", "div_class_value", "items", "called", "is_browser", "scroll_into_view", "el", "enable", "tick", "min", "box", "dispatch", "createEventDispatcher", "i18n", "eta", "queue_position", "queue_size", "status", "scroll_to_output", "timer", "show_progress", "message", "progress", "variant", "loading_text", "absolute", "translucent", "border", "autoscroll", "_timer", "timer_start", "timer_diff", "old_eta", "eta_from_start", "eta_level", "progress_level", "last_progress_level", "progress_bar", "show_eta_bar", "start_timer", "$$invalidate", "formatted_eta", "stop_timer", "onDestroy", "$$value", "p", "formatted_timer"], "mappings": "gVAEO,MAAMA,GAAY,OAAO,OAAW,IAGjC,IAACC,GAAMD,GAAY,IAAM,OAAO,YAAY,MAAQ,IAAM,KAAK,IAAM,EAEpEE,GAAMF,GAAaG,GAAO,sBAAsBA,CAAE,EAAIC,GCLjE,MAAMC,EAAQ,IAAI,IAMlB,SAASC,GAAUL,EAAK,CACvBI,EAAM,QAASE,GAAS,CAClBA,EAAK,EAAEN,CAAG,IACdI,EAAM,OAAOE,CAAI,EACjBA,EAAK,EAAC,EAET,CAAE,EACGF,EAAM,OAAS,GAAGH,GAAII,EAAS,CACpC,CAgBO,SAASE,GAAKC,EAAU,CAE9B,IAAIF,EACJ,OAAIF,EAAM,OAAS,GAAGH,GAAII,EAAS,EAC5B,CACN,QAAS,IAAI,QAASI,GAAY,CACjCL,EAAM,IAAKE,EAAO,CAAE,EAAGE,EAAU,EAAGC,CAAO,EAC9C,CAAG,EACD,OAAQ,CACPL,EAAM,OAAOE,CAAI,CACjB,CACH,CACA,CC5CO,SAASI,EAAUC,EAAqB,CAC1C,IAAAC,EAAQ,CAAC,GAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAC9CC,EAAI,EACR,KAAOF,EAAM,KAAQE,EAAID,EAAM,OAAS,GAChCD,GAAA,IACPE,IAEG,IAAAC,EAAOF,EAAMC,CAAC,EACV,OAAA,OAAO,UAAUF,CAAG,EAAIA,EAAMA,EAAI,QAAQ,CAAC,GAAKG,CACzD,CCLO,SAASC,GAAQC,EAAK,CAC5B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAG,IAAM,eAChD,CCMA,SAASC,GAAYC,EAAKC,EAAYC,EAAeC,EAAc,CAClE,GAAI,OAAOD,GAAkB,UAAYL,GAAQK,CAAa,EAAG,CAEhE,MAAME,EAAQD,EAAeD,EAEvBG,GAAYH,EAAgBD,IAAeD,EAAI,IAAM,EAAI,IACzDM,EAASN,EAAI,KAAK,UAAYI,EAC9BG,EAASP,EAAI,KAAK,QAAUK,EAC5BG,GAAgBF,EAASC,GAAUP,EAAI,SACvCS,GAAKJ,EAAWG,GAAgBR,EAAI,GAC1C,OAAI,KAAK,IAAIS,CAAC,EAAIT,EAAI,KAAK,WAAa,KAAK,IAAII,CAAK,EAAIJ,EAAI,KAAK,UAC3DG,GAEPH,EAAI,QAAU,GAEPH,GAAQK,CAAa,EAAI,IAAI,KAAKA,EAAc,QAAO,EAAKO,CAAC,EAAIP,EAAgBO,EAEzF,KAAM,IAAI,MAAM,QAAQP,CAAa,EAErC,OAAOA,EAAc,IAAI,CAACQ,EAAGf,IAC5BI,GAAYC,EAAKC,EAAWN,CAAC,EAAGO,EAAcP,CAAC,EAAGQ,EAAaR,CAAC,CAAC,CACpE,EACQ,GAAI,OAAOO,GAAkB,SAAU,CAC7C,MAAMS,EAAa,CAAA,EACnB,UAAWC,KAAKV,EAEfS,EAAWC,CAAC,EAAIb,GAAYC,EAAKC,EAAWW,CAAC,EAAGV,EAAcU,CAAC,EAAGT,EAAaS,CAAC,CAAC,EAGlF,OAAOD,CACT,KACE,OAAM,IAAI,MAAM,iBAAiB,OAAOT,CAAa,SAAS,EAEhE,CAWO,SAASI,GAAOO,EAAOC,EAAO,GAAI,CACxC,MAAMC,EAAQC,GAASH,CAAK,EACtB,CAAE,UAAAI,EAAY,IAAM,QAAAC,EAAU,GAAK,UAAAC,EAAY,GAAM,EAAGL,EAE9D,IAAIM,EAEAhC,EAEAiC,EAEApB,EAAaY,EAEbV,EAAeU,EACfS,EAAW,EACXC,EAAyB,EACzBC,EAAc,GAMlB,SAASC,EAAIC,EAAWZ,EAAO,GAAI,CAClCX,EAAeuB,EACf,MAAMC,EAASN,EAAgB,CAAA,EAC/B,OAAIR,GAAS,MAAQC,EAAK,MAASR,EAAO,WAAa,GAAKA,EAAO,SAAW,GAC7EkB,EAAc,GACdJ,EAAYtC,GAAG,EACfmB,EAAayB,EACbX,EAAM,IAAKF,EAAQV,GACZ,QAAQ,YACLW,EAAK,OAEfS,EAAyB,IADZT,EAAK,OAAS,GAAO,GAAM,CAACA,EAAK,MACT,IACrCQ,EAAW,GAEPlC,IACJgC,EAAYtC,GAAG,EACf0C,EAAc,GACdpC,EAAOC,GAAMP,GAAQ,CACpB,GAAI0C,EACH,OAAAA,EAAc,GACdpC,EAAO,KACA,GAERkC,EAAW,KAAK,IAAIA,EAAWC,EAAwB,CAAC,EACxD,MAAMvB,EAAM,CACX,SAAAsB,EACA,KAAMhB,EACN,QAAS,GACT,IAAMxB,EAAMsC,GAAa,GAAM,GACpC,EACUT,EAAaZ,GAAYC,EAAKC,EAAYY,EAAOV,CAAY,EACnE,OAAAiB,EAAYtC,EACZmB,EAAaY,EACbE,EAAM,IAAKF,EAAQF,GACfX,EAAI,UACPZ,EAAO,MAED,CAACY,EAAI,OAChB,CAAI,GAEK,IAAI,QAAS4B,GAAW,CAC9BxC,EAAK,QAAQ,KAAK,IAAM,CACnBuC,IAAUN,GAAeO,GACjC,CAAI,CACJ,CAAG,EACD,CAED,MAAMtB,EAAS,CACd,IAAAmB,EACA,OAAQ,CAACI,EAAIf,IAASW,EAAII,EAAG1B,EAAcU,CAAK,EAAGC,CAAI,EACvD,UAAWC,EAAM,UACjB,UAAAE,EACA,QAAAC,EACA,UAAAC,CACF,EACC,OAAOb,CACR,mOCpIyB,EAAA,OAAA,gxBAwCSwB,EAAAC,EAAA,YAAA,aAAA/B,EAAK,CAAA,EAAA,CAAC,EAAO,OAAAA,KAAK,CAAC,EAAA,KAAA,8iBAoBnB8B,EAAAE,EAAA,YAAA,aAAAhC,EAAQ,CAAA,EAAA,CAAC,EAAO,OAAAA,KAAQ,CAAC,EAAA,KAAA,4LA1B3DiC,GA+CKC,EAAAC,EAAAC,CAAA,EA9CJC,EA6CKF,EAAAG,CAAA,EAxCJD,EAmBGC,EAAAP,CAAA,EAlBFM,EAICN,EAAAQ,CAAA,EACDF,EAGCN,EAAAS,CAAA,EACDH,EAICN,EAAAU,CAAA,EACDJ,EAGCN,EAAAW,CAAA,EAEFL,EAmBGC,EAAAN,CAAA,EAlBFK,EAICL,EAAAW,CAAA,EACDN,EAGCL,EAAAY,CAAA,EACDP,EAICL,EAAAa,CAAA,EACDR,EAGCL,EAAAc,CAAA,iBAtC8BhB,EAAAC,EAAA,YAAA,aAAA/B,EAAK,CAAA,EAAA,CAAC,EAAO,OAAAA,KAAK,CAAC,EAAA,KAAA,OAoBnB8B,EAAAE,EAAA,YAAA,aAAAhC,EAAQ,CAAA,EAAA,CAAC,EAAO,OAAAA,KAAQ,CAAC,EAAA,KAAA,kFAzD/C,OAAA+C,EAAS,EAAA,EAAAC,QAEdC,EAAM3C,GAAA,CAAQ,EAAG,CAAC,CAAA,4BAClB4C,EAAS5C,GAAA,CAAQ,EAAG,CAAC,CAAA,sBAEvB,IAAA6C,EAEW,eAAAC,GAAA,OACR,QAAQ,IAAA,CAAKH,EAAI,IAAK,CAAA,IAAK,GAAG,CAAI,EAAAC,EAAO,IAAK,CAAA,KAAA,IAAU,CAAA,CAAA,CAAA,QACxD,QAAQ,IAAA,CAAKD,EAAI,IAAK,CAAA,KAAM,GAAG,CAAI,EAAAC,EAAO,IAAK,CAAA,IAAA,IAAS,CAAA,CAAA,CAAA,QACxD,QAAQ,IAAA,CAAKD,EAAI,IAAK,CAAA,KAAM,CAAC,CAAI,EAAAC,EAAO,IAAK,CAAA,IAAA,EAAO,CAAA,CAAA,CAAA,QACpD,QAAQ,IAAA,CAAKD,EAAI,IAAK,CAAA,IAAK,CAAC,CAAI,EAAAC,EAAO,IAAK,CAAA,KAAM,CAAC,CAAA,CAAA,CAAA,EAG3C,eAAAG,GAAA,CACR,MAAAD,EAAA,EACDD,GAAYE,IAGH,eAAAC,GAAA,OACR,QAAQ,IAAA,CAAKL,EAAI,IAAK,CAAA,IAAK,CAAC,CAAI,EAAAC,EAAO,IAAK,CAAA,KAAM,CAAC,CAAA,CAAA,CAAA,EAEzDG,IAGD,OAAAE,GAAA,KACCD,QACuBH,EAAa,qrBC9BhB,EAAA,OAAA,wCAmDK,EAAA,OAAA,oDAKY,EAAA,OAAA,4NAuPhBK,EAAAxD,KAAK,cAAc,EAAA,+BARhCyD,GACC,MAAAzD,KAAK,cAAc,WAChB,uOAJZiC,EASKC,EAAAC,EAAAC,CAAA,wBACLH,EAAgDC,EAAAwB,EAAAtB,CAAA,sDAPvCuB,EAAA,CAAA,EAAA,IAAAC,EAAA,MAAA5D,KAAK,cAAc,cAOP,CAAA6D,GAAAF,EAAA,CAAA,EAAA,IAAAH,KAAAA,EAAAxD,KAAK,cAAc,EAAA,KAAA8D,EAAAC,EAAAP,CAAA,qPAnFnCQ,EAAAhE,OAAY,WAAaA,EAAgB,EAAA,GAAAA,OAAkB,QAAMiE,GAAAjE,CAAA,qBAWhEA,EAAQ,CAAA,EAAA,OAAAkE,MAWHlE,EAAc,CAAA,IAAK,MAAQA,OAAe,QAAaA,EAAc,CAAA,GAAI,EAAC,OAAAmE,GAE1E,GAAAnE,OAAmB,EAAC,OAAAoE,0BAIzBpE,EAAK,CAAA,GAAAqE,GAAArE,CAAA,uCAKN,OAAAA,OAAuB,KAAI,EA+BtBA,OAAkB,OAAM,wCAI5BA,EAAK,CAAA,GAAAsE,GAAAtE,CAAA,8IA7DcuE,EAAApC,EAAA,mBAAAnC,OAAY,QAAQ,EAC3BuE,EAAApC,EAAA,YAAAnC,OAAY,SAAS,+BAFvCiC,EAyBKC,EAAAC,EAAAC,CAAA,2GA/BApC,OAAY,WAAaA,EAAgB,EAAA,GAAAA,OAAkB,4IA4B1DA,EAAK,CAAA,4EArBcuE,EAAApC,EAAA,mBAAAnC,OAAY,QAAQ,kBAC3BuE,EAAApC,EAAA,YAAAnC,OAAY,SAAS,oKA4DjCA,EAAK,CAAA,6QAjEqBwE,EAAA,eAAAxE,EAAa,EAAA,GAAA,GAAK,IAAM,GAAG,2FAF1DiC,EAGCC,EAAAC,EAAAC,CAAA,UAD8BuB,EAAA,CAAA,EAAA,QAAAa,KAAAA,EAAA,eAAAxE,EAAa,EAAA,GAAA,GAAK,IAAM,GAAG,8EAqB5B,cAE9B,6DAHSwD,EAAAxD,KAAiB,EAAC,0BADyD,SAC5E,aAAoB,GAAC,MAACA,EAAU,CAAA,CAAA,MAAC,IACzC,+DADS2D,EAAA,CAAA,EAAA,GAAAH,KAAAA,EAAAxD,KAAiB,EAAC,KAAA8D,EAAAC,EAAAP,CAAA,cAAGxD,EAAU,CAAA,CAAA,kEAXhCA,EAAQ,CAAA,CAAA,uBAAb,OAAIL,GAAA,oKAACK,EAAQ,CAAA,CAAA,oBAAb,OAAIL,GAAA,EAAA,2HAAJ,qDAOC6D,EAAAxD,MAAE,KAAI,SAAK,6BALPA,EAAC,EAAA,EAAC,QAAU,KAAIyE,0DAKb,KAAG,8IAAVd,EAAA,CAAA,EAAA,KAAAH,KAAAA,EAAAxD,MAAE,KAAI,KAAA8D,EAAAC,EAAAP,CAAA,yDAFL,IAAAkB,EAAAlF,EAAUQ,EAAE,EAAA,EAAA,OAAS,CAAC,EAAA,gDAAtB2D,EAAA,CAAA,EAAA,KAAAe,KAAAA,EAAAlF,EAAUQ,EAAE,EAAA,EAAA,OAAS,CAAC,EAAA,KAAA8D,EAAAa,EAAAD,CAAA,iCAFtB,IAAAE,EAAApF,EAAUQ,EAAE,EAAA,EAAA,OAAS,CAAC,EAAA,SAAIR,EAAUQ,EAAC,EAAA,EAAC,MAAM,EAAA,2BAApB,GAAC,oDAAzB2D,EAAA,CAAA,EAAA,KAAAiB,KAAAA,EAAApF,EAAUQ,EAAE,EAAA,EAAA,OAAS,CAAC,EAAA,KAAA8D,EAAAe,EAAAD,CAAA,mBAAIpF,EAAUQ,EAAC,EAAA,EAAC,MAAM,EAAA,KAAA8D,EAAAgB,EAAAC,CAAA,qDAF1C/E,EAAC,EAAA,EAAC,OAAS,MAAIgF,GAAAhF,CAAA,iEAAfA,EAAC,EAAA,EAAC,OAAS,wHAgBCA,EAAG,CAAA,EAAA,IAAOA,EAAa,EAAA,CAAA,GAAK,sBAA7CA,EAAe,EAAA,CAAA,aAAiC,GAClD,+DADEA,EAAe,EAAA,CAAA,sBAAEA,EAAG,CAAA,EAAA,IAAOA,EAAa,EAAA,CAAA,GAAK,KAAE8D,EAAAC,EAAAP,CAAA,sEAoCjC,MAAA,CAAA,OAAAxD,OAAY,SAAS,qEAArB2D,EAAA,CAAA,EAAA,MAAAsB,EAAA,OAAAjF,OAAY,mIALXkF,EAAA,GAAAlF,MAAsB,GAAG,IAxBnCmF,EAAAnF,MAAY,MAAIoF,GAAApF,CAAA,sRAFvBiC,EA6BKC,EAAAmD,EAAAjD,CAAA,EA5BJC,EAmBKgD,EAAAC,CAAA,wBAELjD,EAMKgD,EAAAE,CAAA,EALJlD,EAICkD,EAAAC,CAAA,mBAzBIxF,MAAY,+DAwBF2D,EAAA,CAAA,EAAA,OAAAuB,KAAAA,EAAA,GAAAlF,MAAsB,GAAG,8FAvBhCA,EAAQ,CAAA,CAAA,uBAAb,OAAIL,GAAA,sKAACK,EAAQ,CAAA,CAAA,oBAAb,OAAIL,GAAA,EAAA,2HAAJ,2DAEKqE,EAAAhE,QAAM,GAACyF,GAAA,IAGPzF,EAAC,EAAA,EAAC,MAAQ,MAAI0F,GAAA1F,CAAA,IAGdA,EAAC,EAAA,EAAC,MAAQ,MAAQA,EAAc,EAAA,GAAIA,EAAc,EAAA,EAACA,EAAC,EAAA,CAAA,GAAK,MAAI2F,GAAA,EAG7DC,EAAA5F,OAAkB,MAAI6F,GAAA7F,CAAA,6KANtBA,EAAC,EAAA,EAAC,MAAQ,uEAGVA,EAAC,EAAA,EAAC,MAAQ,MAAQA,EAAc,EAAA,GAAIA,EAAc,EAAA,EAACA,EAAC,EAAA,CAAA,GAAK,8DAGzDA,OAAkB,oLATX,IAEZ,kDAEE,IAAA0E,EAAA1E,MAAE,KAAI,gDAAN2D,EAAA,CAAA,EAAA,KAAAe,KAAAA,EAAA1E,MAAE,KAAI,KAAA8D,EAAAa,EAAAD,CAAA,sDAE0D,GAElE,yDAEG,KAAO1E,EAAe,EAAA,EAAAA,QAAM,IAAI,QAAQ,CAAC,EAAA,6BAAE,GAC9C,wDADG,KAAOA,EAAe,EAAA,EAAAA,QAAM,IAAI,QAAQ,CAAC,EAAA,KAAA8D,EAAAe,EAAAD,CAAA,iDAXxC5E,EAAC,EAAA,EAAC,MAAQ,MAASA,EAAc,EAAA,GAAIA,EAAc,EAAA,EAACA,EAAC,EAAA,CAAA,GAAK,OAAI8F,GAAA9F,CAAA,iEAA9DA,EAAC,EAAA,EAAC,MAAQ,MAASA,EAAc,EAAA,GAAIA,EAAc,EAAA,EAACA,EAAC,EAAA,CAAA,GAAK,gNA+B/CA,EAAY,CAAA,CAAA,8DAAhCiC,EAAoCC,EAAA6D,EAAA3D,CAAA,+DAAhBpC,EAAY,CAAA,CAAA,4NAtE7B,OAAAA,OAAW,UAAS,EAyEfA,OAAW,QAAO,gEAxFfgG,EAAA7D,EAAA,QAAA8D,EAAA,QAAAjG,SAAUA,EAAa,CAAA,EAAA,gBAAA,cACvBA,EAAM,CAAA,GAClBA,EAAM,CAAA,IAAK,YACXA,EAAa,CAAA,IAAK,UAClBA,EAAM,CAAA,GAAI,WAAW,EACFuE,EAAApC,EAAA,cAAAnC,EAAY,CAAA,IAAA,WAC9BA,OAAW,WAAaA,EAAM,CAAA,IAAK,UACpCA,EACA,EAAA,GAAAA,OAAkB,SAAS,EACVuE,EAAApC,EAAA,aAAAnC,EAAW,CAAA,IAAA,cAAgBA,OAAkB,MAAM,qCAErDA,EAAQ,EAAA,EAAG,WAAa,QAAQ,gBACjCA,EAAQ,EAAA,EAAG,IAAM,iBAAiB,UAblDiC,EAuGKC,EAAAC,EAAAC,CAAA,sMAtGS,CAAAyB,GAAAF,EAAA,CAAA,EAAA,KAAAsC,KAAAA,EAAA,QAAAjG,SAAUA,EAAa,CAAA,EAAA,+DACvBA,EAAM,CAAA,GAClBA,EAAM,CAAA,IAAK,YACXA,EAAa,CAAA,IAAK,UAClBA,EAAM,CAAA,GAAI,WAAW,mBACFuE,EAAApC,EAAA,cAAAnC,EAAY,CAAA,IAAA,WAC9BA,OAAW,WAAaA,EAAM,CAAA,IAAK,UACpCA,EACA,EAAA,GAAAA,OAAkB,SAAS,kBACVuE,EAAApC,EAAA,aAAAnC,EAAW,CAAA,IAAA,cAAgBA,OAAkB,MAAM,iEAErDA,EAAQ,EAAA,EAAG,WAAa,QAAQ,2BACjCA,EAAQ,EAAA,EAAG,IAAM,iBAAiB,gFArN7C,IAAAkG,EAAA,CAAA,EAEAC,GAAS,GAEP,MAAAC,GAAA,OAAoB,OAAW,IAC/BrH,GAAMqH,GACT,OAAO,sBACNpH,GAAA,GAEW,eAAAqH,GACdC,EACAC,EAAyB,GAAA,CAGxB,GAAA,SAAO,kBAAoB,WAC1B,OAAO,kBAAoB,OAASA,IAAW,IAM5C,IADLL,EAAM,KAAKI,CAAE,EACR,CAAAH,GAAQA,GAAS,OAAA,QAGhB,MAAAK,GAAA,EAENzH,GAAA,IAAA,CACK,IAAA0H,EAAA,CAAO,EAAG,CAAC,UAEN9G,EAAI,EAAGA,EAAIuG,EAAM,OAAQvG,IAAA,CAG3B,MAAA+G,EAFUR,EAAMvG,CAAC,EAEH,yBAChBA,IAAM,GAAK+G,EAAI,IAAM,OAAO,SAAWD,EAAI,CAAC,KAC/CA,EAAI,CAAC,EAAIC,EAAI,IAAM,OAAO,QAC1BD,EAAI,CAAC,EAAI9G,GAIX,OAAO,UAAW,IAAK8G,EAAI,CAAC,EAAI,GAAI,SAAU,QAAA,CAAA,EAE9CN,GAAS,GACTD,EAAA,CAAA,+DAgBIS,EAAWC,KAEN,GAAA,CAAA,KAAAC,CAAA,EAAA7D,GACA,IAAA8D,EAAqB,IAAA,EAAA9D,EACrB,CAAA,eAAA+D,CAAA,EAAA/D,EACA,CAAA,WAAAgE,CAAA,EAAAhE,EACA,CAAA,OAAAiE,CAAA,EAAAjE,GAOA,iBAAAkE,EAAmB,EAAA,EAAAlE,GACnB,MAAAmE,EAAQ,EAAA,EAAAnE,GACR,cAAAoE,EAA+C,MAAA,EAAApE,GAC/C,QAAAqE,EAAyB,IAAA,EAAArE,GACzB,SAAAsE,EAAyD,IAAA,EAAAtE,GACzD,QAAAuE,EAAgC,SAAA,EAAAvE,GAChC,aAAAwE,EAAe,YAAA,EAAAxE,GACf,SAAAyE,EAAW,EAAA,EAAAzE,GACX,YAAA0E,EAAc,EAAA,EAAA1E,GACd,OAAA2E,EAAS,EAAA,EAAA3E,EACT,CAAA,WAAA4E,EAAA,EAAA5E,EAEPsD,EAEAuB,EAAS,GACTC,EAAc,EACdC,EAAa,EACbC,EAAyB,KACzBC,EAAgC,KAEhCC,GAA2B,EAC3BC,EAAgD,KAChDC,EACAC,EAAmC,KACnCC,GAAe,GAsCb,MAAAC,GAAA,IAAA,CACLC,EAAA,EAAA1B,EAAA0B,EAAA,GAAMR,OAAUS,EAAgB,IAAA,CAAA,CAAA,EAChCD,EAAA,GAAAV,EAAc,YAAY,IAAA,CAAA,OAC1BC,EAAa,CAAA,EACbF,EAAS,GACTxE,MAGQ,SAAAA,IAAA,CACRtE,GAAA,IAAA,MACCgJ,GAAc,YAAY,MAAQD,GAAe,GAAA,EAC7CD,GAAQxE,OAIL,SAAAqF,IAAA,MACRX,EAAa,CAAA,EACbS,EAAA,EAAA1B,EAAA0B,EAAA,GAAMR,OAAUS,EAAgB,IAAA,CAAA,CAAA,EAE3BZ,IACLA,EAAS,IAGVc,GAAA,IAAA,CACKd,GAAQa,WAgBTD,EAA+B,gDAsGnBJ,EAAYO,iDAqBxBjC,EAAS,cAAc,8CAlFhBL,EAAEsC,2qBAvCR9B,IAAQ,UACXA,EAAMkB,CAAA,EAEHlB,GAAO,MAAQkB,IAAYlB,SAC9BmB,GAAkB,YAAY,IAAQ,EAAAH,GAAe,IAAOhB,CAAA,OAC5D2B,EAAgBR,EAAe,QAAQ,CAAC,CAAA,OACxCD,EAAUlB,CAAA,6BApFZ0B,EAAA,GAAGN,GACFD,IAAmB,MAAQA,GAAkB,GAAM,CAAAF,EAChD,KACA,KAAK,IAAIA,EAAaE,EAAgB,CAAC,CAAA,qBACpCX,GAAY,WAClBgB,GAAe,EAAA,yBAIXhB,GAAY,KACfkB,EAAA,GAAAL,EAAiBb,EAAS,IAAKuB,GAAA,IAC1BA,EAAE,OAAS,MAAQA,EAAE,QAAU,KAC3B,OAAAA,EAAE,MAAQA,EAAE,OACT,GAAAA,EAAE,UAAY,YACjBA,EAAE,iBAKXV,EAAiB,IAAA,EAGdA,QACHC,EAAsBD,EAAeA,EAAe,OAAS,CAAC,CAAA,EAC1DE,IACCD,IAAwB,EAC3BI,EAAA,GAAAH,EAAa,MAAM,WAAa,IAAAA,CAAA,EAEhCG,EAAA,GAAAH,EAAa,MAAM,WAAa,QAAAA,CAAA,SAIlCD,EAAsB,MAAA,sBAgCnBnB,IAAW,UACdsB,KAEAG,8BAICpC,GACFY,IACCD,IAAW,WAAaA,IAAW,aACpCZ,GAAiBC,EAAIsB,EAAU,gDA0B7BY,EAAA,GAAAM,EAAkBf,EAAW,QAAQ,CAAC,CAAA", "x_google_ignoreList": [0, 1, 3, 4]}