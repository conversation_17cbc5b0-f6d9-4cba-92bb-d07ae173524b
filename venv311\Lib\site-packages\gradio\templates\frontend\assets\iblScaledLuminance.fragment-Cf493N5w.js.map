{"version": 3, "file": "iblScaledLuminance.fragment-Cf493N5w.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/iblScaledLuminance.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nconst name = \"iblScaledLuminancePixelShader\";\nconst shader = `#include<helperFunctions>\n#ifdef IBL_USE_CUBE_MAP\nvar iblSourceSampler: sampler;var iblSource: texture_cube<f32>;\n#else\nvar iblSourceSampler: sampler;var iblSource: texture_2d<f32>;\n#endif\nuniform iblHeight: i32;uniform iblWidth: i32;fn fetchLuminance(coords: vec2f)->f32 {\n#ifdef IBL_USE_CUBE_MAP\nvar direction: vec3f=equirectangularToCubemapDirection(coords);var color: vec3f=textureSampleLevel(iblSource,iblSourceSampler,direction,0.0).rgb;\n#else\nvar color: vec3f=textureSampleLevel(iblSource,iblSourceSampler,coords,0.0).rgb;\n#endif\nreturn dot(color,LuminanceEncodeApprox);}\n@fragment\nfn main(input: FragmentInputs)->FragmentOutputs {var deform: f32=sin(input.vUV.y*PI);var luminance: f32=fetchLuminance(input.vUV);fragmentOutputs.color=vec4f(vec3f(deform*luminance),1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const iblScaledLuminancePixelShaderWGSL = { name, shader };\n//# sourceMappingURL=iblScaledLuminance.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "iblScaledLuminancePixelShaderWGSL"], "mappings": "qIAGA,MAAMA,EAAO,gCACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8LAgBVC,EAAY,iBAAiBF,CAAI,IAClCE,EAAY,iBAAiBF,CAAI,EAAIC,GAG7B,MAACE,EAAoC,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}