{"version": 3, "file": "step-Ce-xBr2D.js", "sources": ["../../../../node_modules/.pnpm/d3-color@3.1.0/node_modules/d3-color/src/define.js", "../../../../node_modules/.pnpm/d3-color@3.1.0/node_modules/d3-color/src/color.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/basis.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/basisClosed.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/constant.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/color.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/rgb.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/number.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/string.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/transform/decompose.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/transform/parse.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/transform/index.js", "../../../../node_modules/.pnpm/d3-timer@3.0.1/node_modules/d3-timer/src/timer.js", "../../../../node_modules/.pnpm/d3-path@3.1.0/node_modules/d3-path/src/path.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/constant.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/math.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/path.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/array.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/linear.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/point.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/line.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/noop.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/basis.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/basisClosed.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/basisOpen.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/bundle.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/cardinal.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/cardinalClosed.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/cardinalOpen.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/catmullRom.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/catmullRomClosed.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/catmullRomOpen.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/linearClosed.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/monotone.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/natural.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/step.js"], "sourcesContent": ["export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n", "import define, {extend} from \"./define.js\";\n\nexport function Color() {}\n\nexport var darker = 0.7;\nexport var brighter = 1 / darker;\n\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\",\n    reN = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\",\n    reP = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\",\n    reHex = /^#([0-9a-f]{3,8})$/,\n    reRgbInteger = new RegExp(`^rgb\\\\(${reI},${reI},${reI}\\\\)$`),\n    reRgbPercent = new RegExp(`^rgb\\\\(${reP},${reP},${reP}\\\\)$`),\n    reRgbaInteger = new RegExp(`^rgba\\\\(${reI},${reI},${reI},${reN}\\\\)$`),\n    reRgbaPercent = new RegExp(`^rgba\\\\(${reP},${reP},${reP},${reN}\\\\)$`),\n    reHslPercent = new RegExp(`^hsl\\\\(${reN},${reP},${reP}\\\\)$`),\n    reHslaPercent = new RegExp(`^hsla\\\\(${reN},${reP},${reP},${reN}\\\\)$`);\n\nvar named = {\n  aliceblue: 0xf0f8ff,\n  antiquewhite: 0xfaebd7,\n  aqua: 0x00ffff,\n  aquamarine: 0x7fffd4,\n  azure: 0xf0ffff,\n  beige: 0xf5f5dc,\n  bisque: 0xffe4c4,\n  black: 0x000000,\n  blanchedalmond: 0xffebcd,\n  blue: 0x0000ff,\n  blueviolet: 0x8a2be2,\n  brown: 0xa52a2a,\n  burlywood: 0xdeb887,\n  cadetblue: 0x5f9ea0,\n  chartreuse: 0x7fff00,\n  chocolate: 0xd2691e,\n  coral: 0xff7f50,\n  cornflowerblue: 0x6495ed,\n  cornsilk: 0xfff8dc,\n  crimson: 0xdc143c,\n  cyan: 0x00ffff,\n  darkblue: 0x00008b,\n  darkcyan: 0x008b8b,\n  darkgoldenrod: 0xb8860b,\n  darkgray: 0xa9a9a9,\n  darkgreen: 0x006400,\n  darkgrey: 0xa9a9a9,\n  darkkhaki: 0xbdb76b,\n  darkmagenta: 0x8b008b,\n  darkolivegreen: 0x556b2f,\n  darkorange: 0xff8c00,\n  darkorchid: 0x9932cc,\n  darkred: 0x8b0000,\n  darksalmon: 0xe9967a,\n  darkseagreen: 0x8fbc8f,\n  darkslateblue: 0x483d8b,\n  darkslategray: 0x2f4f4f,\n  darkslategrey: 0x2f4f4f,\n  darkturquoise: 0x00ced1,\n  darkviolet: 0x9400d3,\n  deeppink: 0xff1493,\n  deepskyblue: 0x00bfff,\n  dimgray: 0x696969,\n  dimgrey: 0x696969,\n  dodgerblue: 0x1e90ff,\n  firebrick: 0xb22222,\n  floralwhite: 0xfffaf0,\n  forestgreen: 0x228b22,\n  fuchsia: 0xff00ff,\n  gainsboro: 0xdcdcdc,\n  ghostwhite: 0xf8f8ff,\n  gold: 0xffd700,\n  goldenrod: 0xdaa520,\n  gray: 0x808080,\n  green: 0x008000,\n  greenyellow: 0xadff2f,\n  grey: 0x808080,\n  honeydew: 0xf0fff0,\n  hotpink: 0xff69b4,\n  indianred: 0xcd5c5c,\n  indigo: 0x4b0082,\n  ivory: 0xfffff0,\n  khaki: 0xf0e68c,\n  lavender: 0xe6e6fa,\n  lavenderblush: 0xfff0f5,\n  lawngreen: 0x7cfc00,\n  lemonchiffon: 0xfffacd,\n  lightblue: 0xadd8e6,\n  lightcoral: 0xf08080,\n  lightcyan: 0xe0ffff,\n  lightgoldenrodyellow: 0xfafad2,\n  lightgray: 0xd3d3d3,\n  lightgreen: 0x90ee90,\n  lightgrey: 0xd3d3d3,\n  lightpink: 0xffb6c1,\n  lightsalmon: 0xffa07a,\n  lightseagreen: 0x20b2aa,\n  lightskyblue: 0x87cefa,\n  lightslategray: 0x778899,\n  lightslategrey: 0x778899,\n  lightsteelblue: 0xb0c4de,\n  lightyellow: 0xffffe0,\n  lime: 0x00ff00,\n  limegreen: 0x32cd32,\n  linen: 0xfaf0e6,\n  magenta: 0xff00ff,\n  maroon: 0x800000,\n  mediumaquamarine: 0x66cdaa,\n  mediumblue: 0x0000cd,\n  mediumorchid: 0xba55d3,\n  mediumpurple: 0x9370db,\n  mediumseagreen: 0x3cb371,\n  mediumslateblue: 0x7b68ee,\n  mediumspringgreen: 0x00fa9a,\n  mediumturquoise: 0x48d1cc,\n  mediumvioletred: 0xc71585,\n  midnightblue: 0x191970,\n  mintcream: 0xf5fffa,\n  mistyrose: 0xffe4e1,\n  moccasin: 0xffe4b5,\n  navajowhite: 0xffdead,\n  navy: 0x000080,\n  oldlace: 0xfdf5e6,\n  olive: 0x808000,\n  olivedrab: 0x6b8e23,\n  orange: 0xffa500,\n  orangered: 0xff4500,\n  orchid: 0xda70d6,\n  palegoldenrod: 0xeee8aa,\n  palegreen: 0x98fb98,\n  paleturquoise: 0xafeeee,\n  palevioletred: 0xdb7093,\n  papayawhip: 0xffefd5,\n  peachpuff: 0xffdab9,\n  peru: 0xcd853f,\n  pink: 0xffc0cb,\n  plum: 0xdda0dd,\n  powderblue: 0xb0e0e6,\n  purple: 0x800080,\n  rebeccapurple: 0x663399,\n  red: 0xff0000,\n  rosybrown: 0xbc8f8f,\n  royalblue: 0x4169e1,\n  saddlebrown: 0x8b4513,\n  salmon: 0xfa8072,\n  sandybrown: 0xf4a460,\n  seagreen: 0x2e8b57,\n  seashell: 0xfff5ee,\n  sienna: 0xa0522d,\n  silver: 0xc0c0c0,\n  skyblue: 0x87ceeb,\n  slateblue: 0x6a5acd,\n  slategray: 0x708090,\n  slategrey: 0x708090,\n  snow: 0xfffafa,\n  springgreen: 0x00ff7f,\n  steelblue: 0x4682b4,\n  tan: 0xd2b48c,\n  teal: 0x008080,\n  thistle: 0xd8bfd8,\n  tomato: 0xff6347,\n  turquoise: 0x40e0d0,\n  violet: 0xee82ee,\n  wheat: 0xf5deb3,\n  white: 0xffffff,\n  whitesmoke: 0xf5f5f5,\n  yellow: 0xffff00,\n  yellowgreen: 0x9acd32\n};\n\ndefine(Color, color, {\n  copy(channels) {\n    return Object.assign(new this.constructor, this, channels);\n  },\n  displayable() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHex8: color_formatHex8,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\n\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\n\nfunction color_formatHex8() {\n  return this.rgb().formatHex8();\n}\n\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\n\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\n\nexport default function color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000\n      : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00\n      : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000\n      : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000\n      : null) // invalid hex\n      : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)\n      : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)\n      : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)\n      : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)\n      : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)\n      : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)\n      : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins\n      : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0)\n      : null;\n}\n\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);\n}\n\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\n\nexport function rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb;\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\n\nexport function rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\n\nexport function Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Rgb, rgb, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb() {\n    return this;\n  },\n  clamp() {\n    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));\n  },\n  displayable() {\n    return (-0.5 <= this.r && this.r < 255.5)\n        && (-0.5 <= this.g && this.g < 255.5)\n        && (-0.5 <= this.b && this.b < 255.5)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatHex8: rgb_formatHex8,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\n\nfunction rgb_formatHex() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;\n}\n\nfunction rgb_formatHex8() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;\n}\n\nfunction rgb_formatRgb() {\n  const a = clampa(this.opacity);\n  return `${a === 1 ? \"rgb(\" : \"rgba(\"}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? \")\" : `, ${a})`}`;\n}\n\nfunction clampa(opacity) {\n  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));\n}\n\nfunction clampi(value) {\n  return Math.max(0, Math.min(255, Math.round(value) || 0));\n}\n\nfunction hex(value) {\n  value = clampi(value);\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\n\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\n\nexport function hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl;\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      min = Math.min(r, g, b),\n      max = Math.max(r, g, b),\n      h = NaN,\n      s = max - min,\n      l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\n\nexport function hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Hsl, hsl, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = this.h % 360 + (this.h < 0) * 360,\n        s = isNaN(h) || isNaN(this.s) ? 0 : this.s,\n        l = this.l,\n        m2 = l + (l < 0.5 ? l : 1 - l) * s,\n        m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  clamp() {\n    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));\n  },\n  displayable() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s))\n        && (0 <= this.l && this.l <= 1)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl() {\n    const a = clampa(this.opacity);\n    return `${a === 1 ? \"hsl(\" : \"hsla(\"}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? \")\" : `, ${a})`}`;\n  }\n}));\n\nfunction clamph(value) {\n  value = (value || 0) % 360;\n  return value < 0 ? value + 360 : value;\n}\n\nfunction clampt(value) {\n  return Math.max(0, Math.min(1, value || 0));\n}\n\n/* From FvD 13.37, CSS Color Module Level 3 */\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60\n      : h < 180 ? m2\n      : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60\n      : m1) * 255;\n}\n", "export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "export default x => () => x;\n", "import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n", "import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n", "var degrees = 180 / Math.PI;\n\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\n\nexport default function(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n", "import decompose, {identity} from \"./decompose.js\";\n\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\n\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\n", "import number from \"../number.js\";\nimport {parseCss, parseSvg} from \"./parse.js\";\n\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360; else if (b - a > 180) a += 360; // shortest path\n      q.push({i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n\n  return function(a, b) {\n    var s = [], // string constants and placeholders\n        q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\n\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\n", "var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n", "const pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction append(strings) {\n  this._ += strings[0];\n  for (let i = 1, n = strings.length; i < n; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  let d = Math.floor(digits);\n  if (!(d >= 0)) throw new Error(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  const k = 10 ** d;\n  return function(strings) {\n    this._ += strings[0];\n    for (let i = 1, n = strings.length; i < n; ++i) {\n      this._ += Math.round(arguments[i] * k) / k + strings[i];\n    }\n  };\n}\n\nexport class Path {\n  constructor(digits) {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n    this._append = digits == null ? append : appendRound(digits);\n  }\n  moveTo(x, y) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._append`Z`;\n    }\n  }\n  lineTo(x, y) {\n    this._append`L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  quadraticCurveTo(x1, y1, x, y) {\n    this._append`Q${+x1},${+y1},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  bezierCurveTo(x1, y1, x2, y2, x, y) {\n    this._append`C${+x1},${+y1},${+x2},${+y2},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arcTo(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._append`M${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._append`L${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      let x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;\n      }\n\n      this._append`A${r},${r},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;\n    }\n  }\n  arc(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._append`M${x0},${y0}`;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._append`L${x0},${y0}`;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._append`A${r},${r},0,1,${cw},${x - dx},${y - dy}A${r},${r},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._append`A${r},${r},0,${+(da >= pi)},${cw},${this._x1 = x + r * Math.cos(a1)},${this._y1 = y + r * Math.sin(a1)}`;\n    }\n  }\n  rect(x, y, w, h) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${w = +w}v${+h}h${-w}Z`;\n  }\n  toString() {\n    return this._;\n  }\n}\n\nexport function path() {\n  return new Path;\n}\n\n// Allow instanceof d3.path\npath.prototype = Path.prototype;\n\nexport function pathRound(digits = 3) {\n  return new Path(+digits);\n}\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export const abs = Math.abs;\nexport const atan2 = Math.atan2;\nexport const cos = Math.cos;\nexport const max = Math.max;\nexport const min = Math.min;\nexport const sin = Math.sin;\nexport const sqrt = Math.sqrt;\n\nexport const epsilon = 1e-12;\nexport const pi = Math.PI;\nexport const halfPi = pi / 2;\nexport const tau = 2 * pi;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}\n", "import {Path} from \"d3-path\";\n\nexport function withPath(shape) {\n  let digits = 3;\n\n  shape.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n\n  return () => new Path(digits);\n}\n", "export var slice = Array.prototype.slice;\n\nexport default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n", "function Linear(context) {\n  this._context = context;\n}\n\nLinear.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // falls through\n      default: this._context.lineTo(x, y); break;\n    }\n  }\n};\n\nexport default function(context) {\n  return new Linear(context);\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function(x, y) {\n  var defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null,\n      path = withPath(line);\n\n  x = typeof x === \"function\" ? x : (x === undefined) ? pointX : constant(x);\n  y = typeof y === \"function\" ? y : (y === undefined) ? pointY : constant(y);\n\n  function line(data) {\n    var i,\n        n = (data = array(data)).length,\n        d,\n        defined0 = false,\n        buffer;\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) output.lineStart();\n        else output.lineEnd();\n      }\n      if (defined0) output.point(+x(d, i, data), +y(d, i, data));\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  line.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), line) : x;\n  };\n\n  line.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), line) : y;\n  };\n\n  line.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), line) : defined;\n  };\n\n  line.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), line) : curve;\n  };\n\n  line.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), line) : context;\n  };\n\n  return line;\n}\n", "export default function() {}\n", "export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    (2 * that._x0 + that._x1) / 3,\n    (2 * that._y0 + that._y1) / 3,\n    (that._x0 + 2 * that._x1) / 3,\n    (that._y0 + 2 * that._y1) / 3,\n    (that._x0 + 4 * that._x1 + x) / 6,\n    (that._y0 + 4 * that._y1 + y) / 6\n  );\n}\n\nexport function Basis(context) {\n  this._context = context;\n}\n\nBasis.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 3: point(this, this._x1, this._y1); // falls through\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6); // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new Basis(context);\n}\n", "import noop from \"../noop.js\";\nimport {point} from \"./basis.js\";\n\nfunction BasisClosed(context) {\n  this._context = context;\n}\n\nBasisClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x2, this._y2);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);\n        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x2, this._y2);\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x2 = x, this._y2 = y; break;\n      case 1: this._point = 2; this._x3 = x, this._y3 = y; break;\n      case 2: this._point = 3; this._x4 = x, this._y4 = y; this._context.moveTo((this._x0 + 4 * this._x1 + x) / 6, (this._y0 + 4 * this._y1 + y) / 6); break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisClosed(context);\n}\n", "import {point} from \"./basis.js\";\n\nfunction BasisOpen(context) {\n  this._context = context;\n}\n\nBasisOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; var x0 = (this._x0 + 4 * this._x1 + x) / 6, y0 = (this._y0 + 4 * this._y1 + y) / 6; this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisOpen(context);\n}\n", "import {Basis} from \"./basis.js\";\n\nfunction Bundle(context, beta) {\n  this._basis = new Basis(context);\n  this._beta = beta;\n}\n\nBundle.prototype = {\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        j = x.length - 1;\n\n    if (j > 0) {\n      var x0 = x[0],\n          y0 = y[0],\n          dx = x[j] - x0,\n          dy = y[j] - y0,\n          i = -1,\n          t;\n\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(\n          this._beta * x[i] + (1 - this._beta) * (x0 + t * dx),\n          this._beta * y[i] + (1 - this._beta) * (y0 + t * dy)\n        );\n      }\n    }\n\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\nexport default (function custom(beta) {\n\n  function bundle(context) {\n    return beta === 1 ? new Basis(context) : new Bundle(context, beta);\n  }\n\n  bundle.beta = function(beta) {\n    return custom(+beta);\n  };\n\n  return bundle;\n})(0.85);\n", "export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    that._x1 + that._k * (that._x2 - that._x0),\n    that._y1 + that._k * (that._y2 - that._y0),\n    that._x2 + that._k * (that._x1 - x),\n    that._y2 + that._k * (that._y1 - y),\n    that._x2,\n    that._y2\n  );\n}\n\nexport function Cardinal(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinal.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: point(this, this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; this._x1 = x, this._y1 = y; break;\n      case 2: this._point = 3; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new Cardinal(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import noop from \"../noop.js\";\nimport {point} from \"./cardinal.js\";\n\nexport function CardinalClosed(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalClosed(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import {point} from \"./cardinal.js\";\n\nexport function CardinalOpen(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalOpen(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import {epsilon} from \"../math.js\";\nimport {<PERSON>} from \"./cardinal.js\";\n\nexport function point(that, x, y) {\n  var x1 = that._x1,\n      y1 = that._y1,\n      x2 = that._x2,\n      y2 = that._y2;\n\n  if (that._l01_a > epsilon) {\n    var a = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a,\n        n = 3 * that._l01_a * (that._l01_a + that._l12_a);\n    x1 = (x1 * a - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;\n    y1 = (y1 * a - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;\n  }\n\n  if (that._l23_a > epsilon) {\n    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a,\n        m = 3 * that._l23_a * (that._l23_a + that._l12_a);\n    x2 = (x2 * b + that._x1 * that._l23_2a - x * that._l12_2a) / m;\n    y2 = (y2 * b + that._y1 * that._l23_2a - y * that._l12_2a) / m;\n  }\n\n  that._context.bezierCurveTo(x1, y1, x2, y2, that._x2, that._y2);\n}\n\nfunction CatmullRom(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRom.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: this.point(this._x2, this._y2); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; // falls through\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import {CardinalClosed} from \"./cardinalClosed.js\";\nimport noop from \"../noop.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomClosed(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import {<PERSON><PERSON><PERSON>} from \"./cardinalOpen.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomOpen(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import noop from \"../noop.js\";\n\nfunction LinearClosed(context) {\n  this._context = context;\n}\n\nLinearClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._point) this._context.closePath();\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);\n    else this._point = 1, this._context.moveTo(x, y);\n  }\n};\n\nexport default function(context) {\n  return new LinearClosed(context);\n}\n", "function sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\n// Calculate the slopes of the tangents (Hermite-type interpolation) based on\n// the following paper: <PERSON>effen, M. 1990. A Simple Method for Monotonic\n// Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.\n// NOV(II), P. 443, 1990.\nfunction slope3(that, x2, y2) {\n  var h0 = that._x1 - that._x0,\n      h1 = x2 - that._x1,\n      s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),\n      s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),\n      p = (s0 * h1 + s1 * h0) / (h0 + h1);\n  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;\n}\n\n// Calculate a one-sided slope.\nfunction slope2(that, t) {\n  var h = that._x1 - that._x0;\n  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;\n}\n\n// According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations\n// \"you can express cubic Hermite interpolation in terms of cubic Bézier curves\n// with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1\".\nfunction point(that, t0, t1) {\n  var x0 = that._x0,\n      y0 = that._y0,\n      x1 = that._x1,\n      y1 = that._y1,\n      dx = (x1 - x0) / 3;\n  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);\n}\n\nfunction MonotoneX(context) {\n  this._context = context;\n}\n\nMonotoneX.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 =\n    this._t0 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n      case 3: point(this, this._t0, slope2(this, this._t0)); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    var t1 = NaN;\n\n    x = +x, y = +y;\n    if (x === this._x1 && y === this._y1) return; // Ignore coincident points.\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; point(this, slope2(this, t1 = slope3(this, x, y)), t1); break;\n      default: point(this, this._t0, t1 = slope3(this, x, y)); break;\n    }\n\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n    this._t0 = t1;\n  }\n}\n\nfunction MonotoneY(context) {\n  this._context = new ReflectContext(context);\n}\n\n(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x, y) {\n  MonotoneX.prototype.point.call(this, y, x);\n};\n\nfunction ReflectContext(context) {\n  this._context = context;\n}\n\nReflectContext.prototype = {\n  moveTo: function(x, y) { this._context.moveTo(y, x); },\n  closePath: function() { this._context.closePath(); },\n  lineTo: function(x, y) { this._context.lineTo(y, x); },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) { this._context.bezierCurveTo(y1, x1, y2, x2, y, x); }\n};\n\nexport function monotoneX(context) {\n  return new MonotoneX(context);\n}\n\nexport function monotoneY(context) {\n  return new MonotoneY(context);\n}\n", "function Natural(context) {\n  this._context = context;\n}\n\nNatural.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        n = x.length;\n\n    if (n) {\n      this._line ? this._context.lineTo(x[0], y[0]) : this._context.moveTo(x[0], y[0]);\n      if (n === 2) {\n        this._context.lineTo(x[1], y[1]);\n      } else {\n        var px = controlPoints(x),\n            py = controlPoints(y);\n        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {\n          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x[i1], y[i1]);\n        }\n      }\n    }\n\n    if (this._line || (this._line !== 0 && n === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n    this._x = this._y = null;\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\n// See https://www.particleincell.com/2012/bezier-splines/ for derivation.\nfunction controlPoints(x) {\n  var i,\n      n = x.length - 1,\n      m,\n      a = new Array(n),\n      b = new Array(n),\n      r = new Array(n);\n  a[0] = 0, b[0] = 2, r[0] = x[0] + 2 * x[1];\n  for (i = 1; i < n - 1; ++i) a[i] = 1, b[i] = 4, r[i] = 4 * x[i] + 2 * x[i + 1];\n  a[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x[n - 1] + x[n];\n  for (i = 1; i < n; ++i) m = a[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];\n  a[n - 1] = r[n - 1] / b[n - 1];\n  for (i = n - 2; i >= 0; --i) a[i] = (r[i] - a[i + 1]) / b[i];\n  b[n - 1] = (x[n] + a[n - 1]) / 2;\n  for (i = 0; i < n - 1; ++i) b[i] = 2 * x[i + 1] - a[i + 1];\n  return [a, b];\n}\n\nexport default function(context) {\n  return new Natural(context);\n}\n", "function Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\n\nStep.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // falls through\n      default: {\n        if (this._t <= 0) {\n          this._context.lineTo(this._x, y);\n          this._context.lineTo(x, y);\n        } else {\n          var x1 = this._x * (1 - this._t) + x * this._t;\n          this._context.lineTo(x1, this._y);\n          this._context.lineTo(x1, y);\n        }\n        break;\n      }\n    }\n    this._x = x, this._y = y;\n  }\n};\n\nexport default function(context) {\n  return new Step(context, 0.5);\n}\n\nexport function stepBefore(context) {\n  return new Step(context, 0);\n}\n\nexport function stepAfter(context) {\n  return new Step(context, 1);\n}\n"], "names": ["define", "constructor", "factory", "prototype", "extend", "parent", "definition", "key", "Color", "darker", "brighter", "reI", "reN", "reP", "reHex", "reRgbInteger", "reRgbPercent", "reRgbaInteger", "reRgbaPercent", "reHslPercent", "reHslaPercent", "named", "color", "channels", "color_formatHex", "color_formatHex8", "color_formatHsl", "color_formatRgb", "hslConvert", "format", "m", "l", "rgbn", "Rgb", "rgba", "hsla", "n", "r", "g", "b", "a", "rgbConvert", "o", "rgb", "opacity", "k", "clampi", "clampa", "rgb_formatHex", "rgb_formatHex8", "rgb_formatRgb", "hex", "value", "h", "s", "Hsl", "min", "max", "hsl", "m2", "m1", "hsl2rgb", "clamph", "clampt", "basis", "t1", "v0", "v1", "v2", "v3", "t2", "t3", "basis$1", "values", "t", "i", "basisClosed", "constant$1", "x", "linear", "d", "exponential", "y", "hue", "constant", "gamma", "nogamma", "interpolateRgb", "rgbGamma", "start", "end", "colorRgb", "rgbSpline", "spline", "colors", "rgbBasis", "rgbBasisClosed", "interpolateNumber", "reA", "reB", "zero", "one", "interpolateString", "bi", "am", "bm", "bs", "q", "number", "degrees", "identity", "decompose", "c", "e", "f", "scaleX", "scaleY", "skewX", "svgNode", "parseCss", "parseSvg", "interpolateTransform", "parse", "pxComma", "pxParen", "degParen", "pop", "translate", "xa", "ya", "xb", "yb", "rotate", "scale", "interpolateTransformCss", "interpolateTransformSvg", "frame", "timeout", "interval", "poke<PERSON><PERSON><PERSON>", "taskHead", "taskTail", "clockLast", "clockNow", "clockSkew", "clock", "set<PERSON>rame", "now", "clearNow", "Timer", "timer", "callback", "delay", "time", "sleep", "timer<PERSON><PERSON><PERSON>", "wake", "nap", "poke", "t0", "pi", "tau", "epsilon", "tauEpsilon", "append", "strings", "appendRound", "digits", "Path", "x1", "y1", "x2", "y2", "x0", "y0", "x21", "y21", "x01", "y01", "l01_2", "x20", "y20", "l21_2", "l20_2", "l21", "l01", "t01", "t21", "a0", "a1", "ccw", "dx", "dy", "cw", "da", "w", "path", "abs", "atan2", "cos", "sin", "sqrt", "halfPi", "acos", "asin", "with<PERSON><PERSON>", "shape", "_", "array", "Linear", "context", "curveLinear", "p", "line", "defined", "curve", "output", "pointX", "pointY", "data", "defined0", "buffer", "noop", "point", "that", "<PERSON><PERSON>", "curveBasis", "BasisClosed", "curveBasisClosed", "BasisOpen", "curveBasisOpen", "Bundle", "beta", "j", "curveBundle", "custom", "bundle", "<PERSON>", "tension", "curveCardinal", "cardinal", "CardinalClosed", "curveCardinalClosed", "<PERSON><PERSON><PERSON>", "curveCardinalOpen", "CatmullRom", "alpha", "x23", "y23", "curveCatmullRom", "catmullRom", "CatmullRomClosed", "curveCatmullRomClosed", "CatmullRomOpen", "curveCatmullRomOpen", "LinearClosed", "curveLinearClosed", "sign", "slope3", "h0", "h1", "s0", "s1", "slope2", "MonotoneX", "MonotoneY", "ReflectContext", "monotoneX", "monotoneY", "Natural", "px", "controlPoints", "py", "i0", "i1", "curveNatural", "Step", "curveStep", "stepBefore", "stepAfter"], "mappings": "AAAe,SAAAA,GAASC,EAAaC,EAASC,EAAW,CACvDF,EAAY,UAAYC,EAAQ,UAAYC,EAC5CA,EAAU,YAAcF,CAC1B,CAEO,SAASG,GAAOC,EAAQC,EAAY,CACzC,IAAIH,EAAY,OAAO,OAAOE,EAAO,SAAS,EAC9C,QAASE,KAAOD,EAAYH,EAAUI,CAAG,EAAID,EAAWC,CAAG,EAC3D,OAAOJ,CACT,CCPO,SAASK,GAAQ,CAAE,CAEhB,IAACC,EAAS,GACTC,EAAW,EAAID,EAEtBE,EAAM,sBACNC,EAAM,oDACNC,EAAM,qDACNC,GAAQ,qBACRC,GAAe,IAAI,OAAO,UAAUJ,CAAG,IAAIA,CAAG,IAAIA,CAAG,MAAM,EAC3DK,GAAe,IAAI,OAAO,UAAUH,CAAG,IAAIA,CAAG,IAAIA,CAAG,MAAM,EAC3DI,GAAgB,IAAI,OAAO,WAAWN,CAAG,IAAIA,CAAG,IAAIA,CAAG,IAAIC,CAAG,MAAM,EACpEM,GAAgB,IAAI,OAAO,WAAWL,CAAG,IAAIA,CAAG,IAAIA,CAAG,IAAID,CAAG,MAAM,EACpEO,GAAe,IAAI,OAAO,UAAUP,CAAG,IAAIC,CAAG,IAAIA,CAAG,MAAM,EAC3DO,GAAgB,IAAI,OAAO,WAAWR,CAAG,IAAIC,CAAG,IAAIA,CAAG,IAAID,CAAG,MAAM,EAEpES,GAAQ,CACV,UAAW,SACX,aAAc,SACd,KAAM,MACN,WAAY,QACZ,MAAO,SACP,MAAO,SACP,OAAQ,SACR,MAAO,EACP,eAAgB,SAChB,KAAM,IACN,WAAY,QACZ,MAAO,SACP,UAAW,SACX,UAAW,QACX,WAAY,QACZ,UAAW,SACX,MAAO,SACP,eAAgB,QAChB,SAAU,SACV,QAAS,SACT,KAAM,MACN,SAAU,IACV,SAAU,MACV,cAAe,SACf,SAAU,SACV,UAAW,MACX,SAAU,SACV,UAAW,SACX,YAAa,QACb,eAAgB,QAChB,WAAY,SACZ,WAAY,SACZ,QAAS,QACT,WAAY,SACZ,aAAc,QACd,cAAe,QACf,cAAe,QACf,cAAe,QACf,cAAe,MACf,WAAY,QACZ,SAAU,SACV,YAAa,MACb,QAAS,QACT,QAAS,QACT,WAAY,QACZ,UAAW,SACX,YAAa,SACb,YAAa,QACb,QAAS,SACT,UAAW,SACX,WAAY,SACZ,KAAM,SACN,UAAW,SACX,KAAM,QACN,MAAO,MACP,YAAa,SACb,KAAM,QACN,SAAU,SACV,QAAS,SACT,UAAW,SACX,OAAQ,QACR,MAAO,SACP,MAAO,SACP,SAAU,SACV,cAAe,SACf,UAAW,QACX,aAAc,SACd,UAAW,SACX,WAAY,SACZ,UAAW,SACX,qBAAsB,SACtB,UAAW,SACX,WAAY,QACZ,UAAW,SACX,UAAW,SACX,YAAa,SACb,cAAe,QACf,aAAc,QACd,eAAgB,QAChB,eAAgB,QAChB,eAAgB,SAChB,YAAa,SACb,KAAM,MACN,UAAW,QACX,MAAO,SACP,QAAS,SACT,OAAQ,QACR,iBAAkB,QAClB,WAAY,IACZ,aAAc,SACd,aAAc,QACd,eAAgB,QAChB,gBAAiB,QACjB,kBAAmB,MACnB,gBAAiB,QACjB,gBAAiB,SACjB,aAAc,QACd,UAAW,SACX,UAAW,SACX,SAAU,SACV,YAAa,SACb,KAAM,IACN,QAAS,SACT,MAAO,QACP,UAAW,QACX,OAAQ,SACR,UAAW,SACX,OAAQ,SACR,cAAe,SACf,UAAW,SACX,cAAe,SACf,cAAe,SACf,WAAY,SACZ,UAAW,SACX,KAAM,SACN,KAAM,SACN,KAAM,SACN,WAAY,SACZ,OAAQ,QACR,cAAe,QACf,IAAK,SACL,UAAW,SACX,UAAW,QACX,YAAa,QACb,OAAQ,SACR,WAAY,SACZ,SAAU,QACV,SAAU,SACV,OAAQ,SACR,OAAQ,SACR,QAAS,QACT,UAAW,QACX,UAAW,QACX,UAAW,QACX,KAAM,SACN,YAAa,MACb,UAAW,QACX,IAAK,SACL,KAAM,MACN,QAAS,SACT,OAAQ,SACR,UAAW,QACX,OAAQ,SACR,MAAO,SACP,MAAO,SACP,WAAY,SACZ,OAAQ,SACR,YAAa,QACf,EAEArB,GAAOQ,EAAOc,GAAO,CACnB,KAAKC,EAAU,CACb,OAAO,OAAO,OAAO,IAAI,KAAK,YAAa,KAAMA,CAAQ,CAC1D,EACD,aAAc,CACZ,OAAO,KAAK,MAAM,aACnB,EACD,IAAKC,GACL,UAAWA,GACX,WAAYC,GACZ,UAAWC,GACX,UAAWC,GACX,SAAUA,EACZ,CAAC,EAED,SAASH,IAAkB,CACzB,OAAO,KAAK,MAAM,WACpB,CAEA,SAASC,IAAmB,CAC1B,OAAO,KAAK,MAAM,YACpB,CAEA,SAASC,IAAkB,CACzB,OAAOE,GAAW,IAAI,EAAE,WAC1B,CAEA,SAASD,IAAkB,CACzB,OAAO,KAAK,MAAM,WACpB,CAEe,SAASL,GAAMO,EAAQ,CACpC,IAAIC,EAAGC,EACP,OAAAF,GAAUA,EAAS,IAAI,KAAM,EAAC,YAAW,GACjCC,EAAIhB,GAAM,KAAKe,CAAM,IAAME,EAAID,EAAE,CAAC,EAAE,OAAQA,EAAI,SAASA,EAAE,CAAC,EAAG,EAAE,EAAGC,IAAM,EAAIC,GAAKF,CAAC,EACtFC,IAAM,EAAI,IAAIE,EAAKH,GAAK,EAAI,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,EAAI,KAASA,EAAI,KAAQ,EAAMA,EAAI,GAAM,CAAC,EAChHC,IAAM,EAAIG,EAAKJ,GAAK,GAAK,IAAMA,GAAK,GAAK,IAAMA,GAAK,EAAI,KAAOA,EAAI,KAAQ,GAAI,EAC/EC,IAAM,EAAIG,EAAMJ,GAAK,GAAK,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,EAAI,MAAUA,EAAI,KAAQ,EAAMA,EAAI,IAAQ,GAAI,EACtJ,OACCA,EAAIf,GAAa,KAAKc,CAAM,GAAK,IAAII,EAAIH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAG,CAAC,GAC5DA,EAAId,GAAa,KAAKa,CAAM,GAAK,IAAII,EAAIH,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAK,CAAC,GAChGA,EAAIb,GAAc,KAAKY,CAAM,GAAKK,EAAKJ,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,GAC7DA,EAAIZ,GAAc,KAAKW,CAAM,GAAKK,EAAKJ,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,CAAC,GACjGA,EAAIX,GAAa,KAAKU,CAAM,GAAKM,GAAKL,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,EAAI,IAAK,CAAC,GACrEA,EAAIV,GAAc,KAAKS,CAAM,GAAKM,GAAKL,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,CAAC,EAC1ET,GAAM,eAAeQ,CAAM,EAAIG,GAAKX,GAAMQ,CAAM,CAAC,EACjDA,IAAW,cAAgB,IAAII,EAAI,IAAK,IAAK,IAAK,CAAC,EACnD,IACR,CAEA,SAASD,GAAKI,EAAG,CACf,OAAO,IAAIH,EAAIG,GAAK,GAAK,IAAMA,GAAK,EAAI,IAAMA,EAAI,IAAM,CAAC,CAC3D,CAEA,SAASF,EAAKG,EAAGC,EAAGC,EAAGC,EAAG,CACxB,OAAIA,GAAK,IAAGH,EAAIC,EAAIC,EAAI,KACjB,IAAIN,EAAII,EAAGC,EAAGC,EAAGC,CAAC,CAC3B,CAEO,SAASC,GAAWC,EAAG,CAE5B,OADMA,aAAalC,IAAQkC,EAAIpB,GAAMoB,CAAC,GACjCA,GACLA,EAAIA,EAAE,MACC,IAAIT,EAAIS,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,GAFxB,IAAIT,CAGrB,CAEO,SAASU,EAAIN,EAAGC,EAAGC,EAAGK,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIH,GAAWJ,CAAC,EAAI,IAAIJ,EAAII,EAAGC,EAAGC,EAAGK,GAAkB,CAAW,CAChG,CAEO,SAASX,EAAII,EAAGC,EAAGC,EAAGK,EAAS,CACpC,KAAK,EAAI,CAACP,EACV,KAAK,EAAI,CAACC,EACV,KAAK,EAAI,CAACC,EACV,KAAK,QAAU,CAACK,CAClB,CAEA5C,GAAOiC,EAAKU,EAAKvC,GAAOI,EAAO,CAC7B,SAASqC,EAAG,CACV,OAAAA,EAAIA,GAAK,KAAOnC,EAAW,KAAK,IAAIA,EAAUmC,CAAC,EACxC,IAAIZ,EAAI,KAAK,EAAIY,EAAG,KAAK,EAAIA,EAAG,KAAK,EAAIA,EAAG,KAAK,OAAO,CAChE,EACD,OAAOA,EAAG,CACR,OAAAA,EAAIA,GAAK,KAAOpC,EAAS,KAAK,IAAIA,EAAQoC,CAAC,EACpC,IAAIZ,EAAI,KAAK,EAAIY,EAAG,KAAK,EAAIA,EAAG,KAAK,EAAIA,EAAG,KAAK,OAAO,CAChE,EACD,KAAM,CACJ,OAAO,IACR,EACD,OAAQ,CACN,OAAO,IAAIZ,EAAIa,EAAO,KAAK,CAAC,EAAGA,EAAO,KAAK,CAAC,EAAGA,EAAO,KAAK,CAAC,EAAGC,EAAO,KAAK,OAAO,CAAC,CACpF,EACD,aAAc,CACZ,MAAQ,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,GAAK,KAAK,SAAW,KAAK,SAAW,CAC9C,EACD,IAAKC,GACL,UAAWA,GACX,WAAYC,GACZ,UAAWC,GACX,SAAUA,EACZ,CAAC,CAAC,EAEF,SAASF,IAAgB,CACvB,MAAO,IAAIG,EAAI,KAAK,CAAC,CAAC,GAAGA,EAAI,KAAK,CAAC,CAAC,GAAGA,EAAI,KAAK,CAAC,CAAC,EACpD,CAEA,SAASF,IAAiB,CACxB,MAAO,IAAIE,EAAI,KAAK,CAAC,CAAC,GAAGA,EAAI,KAAK,CAAC,CAAC,GAAGA,EAAI,KAAK,CAAC,CAAC,GAAGA,GAAK,MAAM,KAAK,OAAO,EAAI,EAAI,KAAK,SAAW,GAAG,CAAC,EAC1G,CAEA,SAASD,IAAgB,CACvB,MAAMV,EAAIO,EAAO,KAAK,OAAO,EAC7B,MAAO,GAAGP,IAAM,EAAI,OAAS,OAAO,GAAGM,EAAO,KAAK,CAAC,CAAC,KAAKA,EAAO,KAAK,CAAC,CAAC,KAAKA,EAAO,KAAK,CAAC,CAAC,GAAGN,IAAM,EAAI,IAAM,KAAKA,CAAC,GAAG,EACzH,CAEA,SAASO,EAAOH,EAAS,CACvB,OAAO,MAAMA,CAAO,EAAI,EAAI,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,CAAO,CAAC,CAC9D,CAEA,SAASE,EAAOM,EAAO,CACrB,OAAO,KAAK,IAAI,EAAG,KAAK,IAAI,IAAK,KAAK,MAAMA,CAAK,GAAK,CAAC,CAAC,CAC1D,CAEA,SAASD,EAAIC,EAAO,CAClB,OAAAA,EAAQN,EAAOM,CAAK,GACZA,EAAQ,GAAK,IAAM,IAAMA,EAAM,SAAS,EAAE,CACpD,CAEA,SAASjB,GAAKkB,EAAGC,EAAGvB,EAAGS,EAAG,CACxB,OAAIA,GAAK,EAAGa,EAAIC,EAAIvB,EAAI,IACfA,GAAK,GAAKA,GAAK,EAAGsB,EAAIC,EAAI,IAC1BA,GAAK,IAAGD,EAAI,KACd,IAAIE,EAAIF,EAAGC,EAAGvB,EAAGS,CAAC,CAC3B,CAEO,SAASZ,GAAWc,EAAG,CAC5B,GAAIA,aAAaa,EAAK,OAAO,IAAIA,EAAIb,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,EAE7D,GADMA,aAAalC,IAAQkC,EAAIpB,GAAMoB,CAAC,GAClC,CAACA,EAAG,OAAO,IAAIa,EACnB,GAAIb,aAAaa,EAAK,OAAOb,EAC7BA,EAAIA,EAAE,MACN,IAAIL,EAAIK,EAAE,EAAI,IACVJ,EAAII,EAAE,EAAI,IACVH,EAAIG,EAAE,EAAI,IACVc,EAAM,KAAK,IAAInB,EAAGC,EAAGC,CAAC,EACtBkB,EAAM,KAAK,IAAIpB,EAAGC,EAAGC,CAAC,EACtBc,EAAI,IACJC,EAAIG,EAAMD,EACVzB,GAAK0B,EAAMD,GAAO,EACtB,OAAIF,GACEjB,IAAMoB,EAAKJ,GAAKf,EAAIC,GAAKe,GAAKhB,EAAIC,GAAK,EAClCD,IAAMmB,EAAKJ,GAAKd,EAAIF,GAAKiB,EAAI,EACjCD,GAAKhB,EAAIC,GAAKgB,EAAI,EACvBA,GAAKvB,EAAI,GAAM0B,EAAMD,EAAM,EAAIC,EAAMD,EACrCH,GAAK,IAELC,EAAIvB,EAAI,GAAKA,EAAI,EAAI,EAAIsB,EAEpB,IAAIE,EAAIF,EAAGC,EAAGvB,EAAGW,EAAE,OAAO,CACnC,CAEO,SAASgB,GAAIL,EAAGC,EAAGvB,EAAGa,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIhB,GAAWyB,CAAC,EAAI,IAAIE,EAAIF,EAAGC,EAAGvB,EAAGa,GAAkB,CAAW,CAChG,CAEA,SAASW,EAAIF,EAAGC,EAAGvB,EAAGa,EAAS,CAC7B,KAAK,EAAI,CAACS,EACV,KAAK,EAAI,CAACC,EACV,KAAK,EAAI,CAACvB,EACV,KAAK,QAAU,CAACa,CAClB,CAEA5C,GAAOuD,EAAKG,GAAKtD,GAAOI,EAAO,CAC7B,SAASqC,EAAG,CACV,OAAAA,EAAIA,GAAK,KAAOnC,EAAW,KAAK,IAAIA,EAAUmC,CAAC,EACxC,IAAIU,EAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIV,EAAG,KAAK,OAAO,CACxD,EACD,OAAOA,EAAG,CACR,OAAAA,EAAIA,GAAK,KAAOpC,EAAS,KAAK,IAAIA,EAAQoC,CAAC,EACpC,IAAIU,EAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIV,EAAG,KAAK,OAAO,CACxD,EACD,KAAM,CACJ,IAAIQ,EAAI,KAAK,EAAI,KAAO,KAAK,EAAI,GAAK,IAClCC,EAAI,MAAMD,CAAC,GAAK,MAAM,KAAK,CAAC,EAAI,EAAI,KAAK,EACzCtB,EAAI,KAAK,EACT4B,EAAK5B,GAAKA,EAAI,GAAMA,EAAI,EAAIA,GAAKuB,EACjCM,EAAK,EAAI7B,EAAI4B,EACjB,OAAO,IAAI1B,EACT4B,EAAQR,GAAK,IAAMA,EAAI,IAAMA,EAAI,IAAKO,EAAID,CAAE,EAC5CE,EAAQR,EAAGO,EAAID,CAAE,EACjBE,EAAQR,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAKO,EAAID,CAAE,EAC3C,KAAK,OACX,CACG,EACD,OAAQ,CACN,OAAO,IAAIJ,EAAIO,GAAO,KAAK,CAAC,EAAGC,EAAO,KAAK,CAAC,EAAGA,EAAO,KAAK,CAAC,EAAGhB,EAAO,KAAK,OAAO,CAAC,CACpF,EACD,aAAc,CACZ,OAAQ,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,MAAM,KAAK,CAAC,IAC1C,GAAK,KAAK,GAAK,KAAK,GAAK,GACzB,GAAK,KAAK,SAAW,KAAK,SAAW,CAC9C,EACD,WAAY,CACV,MAAMP,EAAIO,EAAO,KAAK,OAAO,EAC7B,MAAO,GAAGP,IAAM,EAAI,OAAS,OAAO,GAAGsB,GAAO,KAAK,CAAC,CAAC,KAAKC,EAAO,KAAK,CAAC,EAAI,GAAG,MAAMA,EAAO,KAAK,CAAC,EAAI,GAAG,IAAIvB,IAAM,EAAI,IAAM,KAAKA,CAAC,GAAG,EACtI,CACH,CAAC,CAAC,EAEF,SAASsB,GAAOV,EAAO,CACrB,OAAAA,GAASA,GAAS,GAAK,IAChBA,EAAQ,EAAIA,EAAQ,IAAMA,CACnC,CAEA,SAASW,EAAOX,EAAO,CACrB,OAAO,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,GAAS,CAAC,CAAC,CAC5C,CAGA,SAASS,EAAQR,EAAGO,EAAID,EAAI,CAC1B,OAAQN,EAAI,GAAKO,GAAMD,EAAKC,GAAMP,EAAI,GAChCA,EAAI,IAAMM,EACVN,EAAI,IAAMO,GAAMD,EAAKC,IAAO,IAAMP,GAAK,GACvCO,GAAM,GACd,CC3YO,SAASI,GAAMC,EAAIC,EAAIC,EAAIC,EAAIC,EAAI,CACxC,IAAIC,EAAKL,EAAKA,EAAIM,EAAKD,EAAKL,EAC5B,QAAS,EAAI,EAAIA,EAAK,EAAIK,EAAKC,GAAML,GAC9B,EAAI,EAAII,EAAK,EAAIC,GAAMJ,GACvB,EAAI,EAAIF,EAAK,EAAIK,EAAK,EAAIC,GAAMH,EACjCG,EAAKF,GAAM,CACnB,CAEe,SAAQG,GAACC,EAAQ,CAC9B,IAAIrC,EAAIqC,EAAO,OAAS,EACxB,OAAO,SAASC,EAAG,CACjB,IAAIC,EAAID,GAAK,EAAKA,EAAI,EAAKA,GAAK,GAAKA,EAAI,EAAGtC,EAAI,GAAK,KAAK,MAAMsC,EAAItC,CAAC,EACjE+B,EAAKM,EAAOE,CAAC,EACbP,EAAKK,EAAOE,EAAI,CAAC,EACjBT,EAAKS,EAAI,EAAIF,EAAOE,EAAI,CAAC,EAAI,EAAIR,EAAKC,EACtCC,EAAKM,EAAIvC,EAAI,EAAIqC,EAAOE,EAAI,CAAC,EAAI,EAAIP,EAAKD,EAC9C,OAAOH,IAAOU,EAAIC,EAAIvC,GAAKA,EAAG8B,EAAIC,EAAIC,EAAIC,CAAE,CAChD,CACA,CChBe,SAAQO,GAACH,EAAQ,CAC9B,IAAIrC,EAAIqC,EAAO,OACf,OAAO,SAASC,EAAG,CACjB,IAAIC,EAAI,KAAK,QAAQD,GAAK,GAAK,EAAI,EAAEA,EAAIA,GAAKtC,CAAC,EAC3C8B,EAAKO,GAAQE,EAAIvC,EAAI,GAAKA,CAAC,EAC3B+B,EAAKM,EAAOE,EAAIvC,CAAC,EACjBgC,EAAKK,GAAQE,EAAI,GAAKvC,CAAC,EACvBiC,EAAKI,GAAQE,EAAI,GAAKvC,CAAC,EAC3B,OAAO4B,IAAOU,EAAIC,EAAIvC,GAAKA,EAAG8B,EAAIC,EAAIC,EAAIC,CAAE,CAChD,CACA,CCZA,MAAeQ,GAAAC,GAAK,IAAMA,ECE1B,SAASC,GAAOvC,EAAGwC,EAAG,CACpB,OAAO,SAASN,EAAG,CACjB,OAAOlC,EAAIkC,EAAIM,CACnB,CACA,CAEA,SAASC,GAAYzC,EAAGD,EAAG2C,EAAG,CAC5B,OAAO1C,EAAI,KAAK,IAAIA,EAAG0C,CAAC,EAAG3C,EAAI,KAAK,IAAIA,EAAG2C,CAAC,EAAI1C,EAAG0C,EAAI,EAAIA,EAAG,SAASR,EAAG,CACxE,OAAO,KAAK,IAAIlC,EAAIkC,EAAInC,EAAG2C,CAAC,CAChC,CACA,CAEO,SAASC,GAAI3C,EAAGD,EAAG,CACxB,IAAIyC,EAAIzC,EAAIC,EACZ,OAAOwC,EAAID,GAAOvC,EAAGwC,EAAI,KAAOA,EAAI,KAAOA,EAAI,IAAM,KAAK,MAAMA,EAAI,GAAG,EAAIA,CAAC,EAAII,GAAS,MAAM5C,CAAC,EAAID,EAAIC,CAAC,CAC3G,CAEO,SAAS6C,GAAMH,EAAG,CACvB,OAAQA,EAAI,CAACA,IAAO,EAAII,GAAU,SAAS9C,EAAGD,EAAG,CAC/C,OAAOA,EAAIC,EAAIyC,GAAYzC,EAAGD,EAAG2C,CAAC,EAAIE,GAAS,MAAM5C,CAAC,EAAID,EAAIC,CAAC,CACnE,CACA,CAEe,SAAS8C,GAAQ9C,EAAGD,EAAG,CACpC,IAAIyC,EAAIzC,EAAIC,EACZ,OAAOwC,EAAID,GAAOvC,EAAGwC,CAAC,EAAII,GAAS,MAAM5C,CAAC,EAAID,EAAIC,CAAC,CACrD,CCvBA,MAAA+C,GAAgB,SAASC,EAASN,EAAG,CACnC,IAAI5D,EAAQ+D,GAAMH,CAAC,EAEnB,SAASvC,EAAI8C,EAAOC,EAAK,CACvB,IAAIrD,EAAIf,GAAOmE,EAAQE,EAASF,CAAK,GAAG,GAAIC,EAAMC,EAASD,CAAG,GAAG,CAAC,EAC9DpD,EAAIhB,EAAMmE,EAAM,EAAGC,EAAI,CAAC,EACxBnD,EAAIjB,EAAMmE,EAAM,EAAGC,EAAI,CAAC,EACxB9C,EAAU0C,GAAQG,EAAM,QAASC,EAAI,OAAO,EAChD,OAAO,SAAShB,EAAG,CACjB,OAAAe,EAAM,EAAIpD,EAAEqC,CAAC,EACbe,EAAM,EAAInD,EAAEoC,CAAC,EACbe,EAAM,EAAIlD,EAAEmC,CAAC,EACbe,EAAM,QAAU7C,EAAQ8B,CAAC,EAClBe,EAAQ,EACrB,CACG,CAED9C,OAAAA,EAAI,MAAQ6C,EAEL7C,CACT,EAAG,CAAC,EAEJ,SAASiD,GAAUC,EAAQ,CACzB,OAAO,SAASC,EAAQ,CACtB,IAAI,EAAIA,EAAO,OACXzD,EAAI,IAAI,MAAM,CAAC,EACfC,EAAI,IAAI,MAAM,CAAC,EACfC,EAAI,IAAI,MAAM,CAAC,EACfoC,EAAGrD,EACP,IAAKqD,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACnBrD,EAAQqE,EAASG,EAAOnB,CAAC,CAAC,EAC1BtC,EAAEsC,CAAC,EAAIrD,EAAM,GAAK,EAClBgB,EAAEqC,CAAC,EAAIrD,EAAM,GAAK,EAClBiB,EAAEoC,CAAC,EAAIrD,EAAM,GAAK,EAEpB,OAAAe,EAAIwD,EAAOxD,CAAC,EACZC,EAAIuD,EAAOvD,CAAC,EACZC,EAAIsD,EAAOtD,CAAC,EACZjB,EAAM,QAAU,EACT,SAASoD,EAAG,CACjB,OAAApD,EAAM,EAAIe,EAAEqC,CAAC,EACbpD,EAAM,EAAIgB,EAAEoC,CAAC,EACbpD,EAAM,EAAIiB,EAAEmC,CAAC,EACNpD,EAAQ,EACrB,CACA,CACA,CAEU,IAACyE,GAAWH,GAAU5B,EAAK,EAC1BgC,GAAiBJ,GAAUhB,EAAW,ECtDlC,SAAAqB,EAASzD,EAAGD,EAAG,CAC5B,OAAOC,EAAI,CAACA,EAAGD,EAAI,CAACA,EAAG,SAASmC,EAAG,CACjC,OAAOlC,GAAK,EAAIkC,GAAKnC,EAAImC,CAC7B,CACA,CCFA,IAAIwB,GAAM,8CACNC,EAAM,IAAI,OAAOD,GAAI,OAAQ,GAAG,EAEpC,SAASE,GAAK7D,EAAG,CACf,OAAO,UAAW,CAChB,OAAOA,CACX,CACA,CAEA,SAAS8D,GAAI9D,EAAG,CACd,OAAO,SAASmC,EAAG,CACjB,OAAOnC,EAAEmC,CAAC,EAAI,EAClB,CACA,CAEe,SAAA4B,GAAS9D,EAAGD,EAAG,CAC5B,IAAIgE,EAAKL,GAAI,UAAYC,EAAI,UAAY,EACrCK,EACAC,EACAC,EACA/B,EAAI,GACJrB,EAAI,CAAE,EACNqD,EAAI,CAAA,EAMR,IAHAnE,EAAIA,EAAI,GAAID,EAAIA,EAAI,IAGZiE,EAAKN,GAAI,KAAK1D,CAAC,KACfiE,EAAKN,EAAI,KAAK5D,CAAC,KAChBmE,EAAKD,EAAG,OAASF,IACpBG,EAAKnE,EAAE,MAAMgE,EAAIG,CAAE,EACfpD,EAAEqB,CAAC,EAAGrB,EAAEqB,CAAC,GAAK+B,EACbpD,EAAE,EAAEqB,CAAC,EAAI+B,IAEXF,EAAKA,EAAG,CAAC,MAAQC,EAAKA,EAAG,CAAC,GACzBnD,EAAEqB,CAAC,EAAGrB,EAAEqB,CAAC,GAAK8B,EACbnD,EAAE,EAAEqB,CAAC,EAAI8B,GAEdnD,EAAE,EAAEqB,CAAC,EAAI,KACTgC,EAAE,KAAK,CAAC,EAAGhC,EAAG,EAAGiC,EAAOJ,EAAIC,CAAE,CAAC,CAAC,GAElCF,EAAKJ,EAAI,UAIX,OAAII,EAAKhE,EAAE,SACTmE,EAAKnE,EAAE,MAAMgE,CAAE,EACXjD,EAAEqB,CAAC,EAAGrB,EAAEqB,CAAC,GAAK+B,EACbpD,EAAE,EAAEqB,CAAC,EAAI+B,GAKTpD,EAAE,OAAS,EAAKqD,EAAE,CAAC,EACpBN,GAAIM,EAAE,CAAC,EAAE,CAAC,EACVP,GAAK7D,CAAC,GACLA,EAAIoE,EAAE,OAAQ,SAASjC,EAAG,CACzB,QAASC,EAAI,EAAGjC,EAAGiC,EAAIpC,EAAG,EAAEoC,EAAGrB,GAAGZ,EAAIiE,EAAEhC,CAAC,GAAG,CAAC,EAAIjC,EAAE,EAAEgC,CAAC,EACtD,OAAOpB,EAAE,KAAK,EAAE,CAC1B,EACA,CC/DA,IAAIuD,GAAU,IAAM,KAAK,GAEdC,GAAW,CACpB,WAAY,EACZ,WAAY,EACZ,OAAQ,EACR,MAAO,EACP,OAAQ,EACR,OAAQ,CACV,EAEe,SAAAC,GAASvE,EAAGD,EAAGyE,EAAGhC,EAAGiC,EAAGC,EAAG,CACxC,IAAIC,EAAQC,EAAQC,EACpB,OAAIF,EAAS,KAAK,KAAK3E,EAAIA,EAAID,EAAIA,CAAC,KAAGC,GAAK2E,EAAQ5E,GAAK4E,IACrDE,EAAQ7E,EAAIwE,EAAIzE,EAAIyC,KAAGgC,GAAKxE,EAAI6E,EAAOrC,GAAKzC,EAAI8E,IAChDD,EAAS,KAAK,KAAKJ,EAAIA,EAAIhC,EAAIA,CAAC,KAAGgC,GAAKI,EAAQpC,GAAKoC,EAAQC,GAASD,GACtE5E,EAAIwC,EAAIzC,EAAIyE,IAAGxE,EAAI,CAACA,EAAGD,EAAI,CAACA,EAAG8E,EAAQ,CAACA,EAAOF,EAAS,CAACA,GACtD,CACL,WAAYF,EACZ,WAAYC,EACZ,OAAQ,KAAK,MAAM3E,EAAGC,CAAC,EAAIqE,GAC3B,MAAO,KAAK,KAAKQ,CAAK,EAAIR,GAC1B,OAAQM,EACR,OAAQC,CACZ,CACA,CCvBA,IAAIE,EAGG,SAASC,GAASnE,EAAO,CAC9B,MAAMtB,EAAI,IAAK,OAAO,WAAc,WAAa,UAAY,iBAAiBsB,EAAQ,EAAE,EACxF,OAAOtB,EAAE,WAAagF,GAAWC,GAAUjF,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CACzE,CAEO,SAAS0F,GAASpE,EAAO,CAI9B,OAHIA,GAAS,OACRkE,IAASA,EAAU,SAAS,gBAAgB,6BAA8B,GAAG,GAClFA,EAAQ,aAAa,YAAalE,CAAK,EACnC,EAAEA,EAAQkE,EAAQ,UAAU,QAAQ,YAAa,IAAUR,IAC/D1D,EAAQA,EAAM,OACP2D,GAAU3D,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,EACvE,CCdA,SAASqE,GAAqBC,EAAOC,EAASC,EAASC,EAAU,CAE/D,SAASC,EAAIxE,EAAG,CACd,OAAOA,EAAE,OAASA,EAAE,IAAK,EAAG,IAAM,EACnC,CAED,SAASyE,EAAUC,EAAIC,EAAIC,EAAIC,EAAI7E,EAAGqD,EAAG,CACvC,GAAIqB,IAAOE,GAAMD,IAAOE,EAAI,CAC1B,IAAIxD,EAAIrB,EAAE,KAAK,aAAc,KAAMqE,EAAS,KAAMC,CAAO,EACzDjB,EAAE,KAAK,CAAC,EAAGhC,EAAI,EAAG,EAAGiC,EAAOoB,EAAIE,CAAE,CAAC,EAAG,CAAC,EAAGvD,EAAI,EAAG,EAAGiC,EAAOqB,EAAIE,CAAE,CAAC,CAAC,CACzE,MAAeD,GAAMC,IACf7E,EAAE,KAAK,aAAe4E,EAAKP,EAAUQ,EAAKP,CAAO,CAEpD,CAED,SAASQ,EAAO5F,EAAGD,EAAGe,EAAGqD,EAAG,CACtBnE,IAAMD,GACJC,EAAID,EAAI,IAAKA,GAAK,IAAcA,EAAIC,EAAI,MAAKA,GAAK,KACtDmE,EAAE,KAAK,CAAC,EAAGrD,EAAE,KAAKwE,EAAIxE,CAAC,EAAI,UAAW,KAAMuE,CAAQ,EAAI,EAAG,EAAGjB,EAAOpE,EAAGD,CAAC,CAAC,CAAC,GAClEA,GACTe,EAAE,KAAKwE,EAAIxE,CAAC,EAAI,UAAYf,EAAIsF,CAAQ,CAE3C,CAED,SAASR,EAAM7E,EAAGD,EAAGe,EAAGqD,EAAG,CACrBnE,IAAMD,EACRoE,EAAE,KAAK,CAAC,EAAGrD,EAAE,KAAKwE,EAAIxE,CAAC,EAAI,SAAU,KAAMuE,CAAQ,EAAI,EAAG,EAAGjB,EAAOpE,EAAGD,CAAC,CAAC,CAAC,EACjEA,GACTe,EAAE,KAAKwE,EAAIxE,CAAC,EAAI,SAAWf,EAAIsF,CAAQ,CAE1C,CAED,SAASQ,EAAML,EAAIC,EAAIC,EAAIC,EAAI7E,EAAGqD,EAAG,CACnC,GAAIqB,IAAOE,GAAMD,IAAOE,EAAI,CAC1B,IAAIxD,EAAIrB,EAAE,KAAKwE,EAAIxE,CAAC,EAAI,SAAU,KAAM,IAAK,KAAM,GAAG,EACtDqD,EAAE,KAAK,CAAC,EAAGhC,EAAI,EAAG,EAAGiC,EAAOoB,EAAIE,CAAE,CAAC,EAAG,CAAC,EAAGvD,EAAI,EAAG,EAAGiC,EAAOqB,EAAIE,CAAE,CAAC,CAAC,CACpE,MAAUD,IAAO,GAAKC,IAAO,IAC5B7E,EAAE,KAAKwE,EAAIxE,CAAC,EAAI,SAAW4E,EAAK,IAAMC,EAAK,GAAG,CAEjD,CAED,OAAO,SAAS3F,EAAGD,EAAG,CACpB,IAAIe,EAAI,CAAE,EACNqD,EAAI,CAAA,EACR,OAAAnE,EAAIkF,EAAMlF,CAAC,EAAGD,EAAImF,EAAMnF,CAAC,EACzBwF,EAAUvF,EAAE,WAAYA,EAAE,WAAYD,EAAE,WAAYA,EAAE,WAAYe,EAAGqD,CAAC,EACtEyB,EAAO5F,EAAE,OAAQD,EAAE,OAAQe,EAAGqD,CAAC,EAC/BU,EAAM7E,EAAE,MAAOD,EAAE,MAAOe,EAAGqD,CAAC,EAC5B0B,EAAM7F,EAAE,OAAQA,EAAE,OAAQD,EAAE,OAAQA,EAAE,OAAQe,EAAGqD,CAAC,EAClDnE,EAAID,EAAI,KACD,SAASmC,EAAG,CAEjB,QADIC,EAAI,GAAIvC,EAAIuE,EAAE,OAAQjE,EACnB,EAAEiC,EAAIvC,GAAGkB,GAAGZ,EAAIiE,EAAEhC,CAAC,GAAG,CAAC,EAAIjC,EAAE,EAAEgC,CAAC,EACvC,OAAOpB,EAAE,KAAK,EAAE,CACtB,CACA,CACA,CAEU,IAACgF,GAA0Bb,GAAqBF,GAAU,OAAQ,MAAO,MAAM,EAC9EgB,GAA0Bd,GAAqBD,GAAU,KAAM,IAAK,GAAG,EC9D9EgB,EAAQ,EACRC,EAAU,EACVC,EAAW,EACXC,GAAY,IACZC,EACAC,EACAC,EAAY,EACZC,EAAW,EACXC,EAAY,EACZC,EAAQ,OAAO,aAAgB,UAAY,YAAY,IAAM,YAAc,KAC3EC,GAAW,OAAO,QAAW,UAAY,OAAO,sBAAwB,OAAO,sBAAsB,KAAK,MAAM,EAAI,SAAShC,EAAG,CAAE,WAAWA,EAAG,EAAE,GAE/I,SAASiC,IAAM,CACpB,OAAOJ,IAAaG,GAASE,EAAQ,EAAGL,EAAWE,EAAM,MAAQD,EACnE,CAEA,SAASI,IAAW,CAClBL,EAAW,CACb,CAEO,SAASM,IAAQ,CACtB,KAAK,MACL,KAAK,MACL,KAAK,MAAQ,IACf,CAEAA,GAAM,UAAYC,GAAM,UAAY,CAClC,YAAaD,GACb,QAAS,SAASE,EAAUC,EAAOC,EAAM,CACvC,GAAI,OAAOF,GAAa,WAAY,MAAM,IAAI,UAAU,4BAA4B,EACpFE,GAAQA,GAAQ,KAAON,GAAG,EAAK,CAACM,IAASD,GAAS,KAAO,EAAI,CAACA,GAC1D,CAAC,KAAK,OAASX,IAAa,OAC1BA,EAAUA,EAAS,MAAQ,KAC1BD,EAAW,KAChBC,EAAW,MAEb,KAAK,MAAQU,EACb,KAAK,MAAQE,EACbC,IACD,EACD,KAAM,UAAW,CACX,KAAK,QACP,KAAK,MAAQ,KACb,KAAK,MAAQ,IACbA,KAEH,CACH,EAEO,SAASJ,GAAMC,EAAUC,EAAOC,EAAM,CAC3C,IAAI/E,EAAI,IAAI2E,GACZ,OAAA3E,EAAE,QAAQ6E,EAAUC,EAAOC,CAAI,EACxB/E,CACT,CAEO,SAASiF,IAAa,CAC3BR,KACA,EAAEX,EAEF,QADI,EAAII,EAAU3B,EACX,IACAA,EAAI8B,EAAW,EAAE,QAAU,GAAG,EAAE,MAAM,KAAK,OAAW9B,CAAC,EAC5D,EAAI,EAAE,MAER,EAAEuB,CACJ,CAEA,SAASoB,IAAO,CACdb,GAAYD,EAAYG,EAAM,IAAG,GAAMD,EACvCR,EAAQC,EAAU,EAClB,GAAI,CACFkB,IACJ,QAAY,CACRnB,EAAQ,EACRqB,KACAd,EAAW,CACZ,CACH,CAEA,SAASe,IAAO,CACd,IAAIX,EAAMF,EAAM,IAAK,EAAEO,EAAQL,EAAML,EACjCU,EAAQb,KAAWK,GAAaQ,EAAOV,EAAYK,EACzD,CAEA,SAASU,IAAM,CAEb,QADIE,EAAI9F,EAAK2E,EAAUtE,EAAImF,EAAO,IAC3BxF,GACDA,EAAG,OACDwF,EAAOxF,EAAG,QAAOwF,EAAOxF,EAAG,OAC/B8F,EAAK9F,EAAIA,EAAKA,EAAG,QAEjBK,EAAKL,EAAG,MAAOA,EAAG,MAAQ,KAC1BA,EAAK8F,EAAKA,EAAG,MAAQzF,EAAKsE,EAAWtE,GAGzCuE,EAAWkB,EACXL,GAAMD,CAAI,CACZ,CAEA,SAASC,GAAMD,EAAM,CACnB,GAAI,CAAAjB,EACJ,CAAIC,IAASA,EAAU,aAAaA,CAAO,GAC3C,IAAIe,EAAQC,EAAOV,EACfS,EAAQ,IACNC,EAAO,MAAUhB,EAAU,WAAWmB,GAAMH,EAAOR,EAAM,MAAQD,CAAS,GAC1EN,IAAUA,EAAW,cAAcA,CAAQ,KAE1CA,IAAUI,EAAYG,EAAM,MAAOP,EAAW,YAAYoB,GAAMnB,EAAS,GAC9EH,EAAQ,EAAGU,GAASU,EAAI,GAE5B,CC7GA,MAAMI,GAAK,KAAK,GACZC,GAAM,EAAID,GACVE,EAAU,KACVC,GAAaF,GAAMC,EAEvB,SAASE,GAAOC,EAAS,CACvB,KAAK,GAAKA,EAAQ,CAAC,EACnB,QAAS,EAAI,EAAG,EAAIA,EAAQ,OAAQ,EAAI,EAAG,EAAE,EAC3C,KAAK,GAAK,UAAU,CAAC,EAAIA,EAAQ,CAAC,CAEtC,CAEA,SAASC,GAAYC,EAAQ,CAC3B,IAAIvF,EAAI,KAAK,MAAMuF,CAAM,EACzB,GAAI,EAAEvF,GAAK,GAAI,MAAM,IAAI,MAAM,mBAAmBuF,CAAM,EAAE,EAC1D,GAAIvF,EAAI,GAAI,OAAOoF,GACnB,MAAMvH,EAAI,IAAMmC,EAChB,OAAO,SAASqF,EAAS,CACvB,KAAK,GAAKA,EAAQ,CAAC,EACnB,QAAS1F,EAAI,EAAGvC,EAAIiI,EAAQ,OAAQ1F,EAAIvC,EAAG,EAAEuC,EAC3C,KAAK,GAAK,KAAK,MAAM,UAAUA,CAAC,EAAI9B,CAAC,EAAIA,EAAIwH,EAAQ1F,CAAC,CAE5D,CACA,CAEO,MAAM6F,EAAK,CAChB,YAAYD,EAAQ,CAClB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,KACtB,KAAK,EAAI,GACT,KAAK,QAAUA,GAAU,KAAOH,GAASE,GAAYC,CAAM,CAC5D,CACD,OAAOzF,EAAGI,EAAG,CACX,KAAK,WAAW,KAAK,IAAM,KAAK,IAAM,CAACJ,CAAC,IAAI,KAAK,IAAM,KAAK,IAAM,CAACI,CAAC,EACrE,CACD,WAAY,CACN,KAAK,MAAQ,OACf,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IACrC,KAAK,WAER,CACD,OAAOJ,EAAGI,EAAG,CACX,KAAK,WAAW,KAAK,IAAM,CAACJ,CAAC,IAAI,KAAK,IAAM,CAACI,CAAC,EAC/C,CACD,iBAAiBuF,EAAIC,EAAI5F,EAAGI,EAAG,CAC7B,KAAK,WAAW,CAACuF,CAAE,IAAI,CAACC,CAAE,IAAI,KAAK,IAAM,CAAC5F,CAAC,IAAI,KAAK,IAAM,CAACI,CAAC,EAC7D,CACD,cAAcuF,EAAIC,EAAIC,EAAIC,EAAI9F,EAAGI,EAAG,CAClC,KAAK,WAAW,CAACuF,CAAE,IAAI,CAACC,CAAE,IAAI,CAACC,CAAE,IAAI,CAACC,CAAE,IAAI,KAAK,IAAM,CAAC9F,CAAC,IAAI,KAAK,IAAM,CAACI,CAAC,EAC3E,CACD,MAAMuF,EAAIC,EAAIC,EAAIC,EAAIvI,EAAG,CAIvB,GAHAoI,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIvI,EAAI,CAACA,EAGzCA,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAoBA,CAAC,EAAE,EAElD,IAAIwI,EAAK,KAAK,IACVC,EAAK,KAAK,IACVC,EAAMJ,EAAKF,EACXO,EAAMJ,EAAKF,EACXO,EAAMJ,EAAKJ,EACXS,EAAMJ,EAAKJ,EACXS,EAAQF,EAAMA,EAAMC,EAAMA,EAG9B,GAAI,KAAK,MAAQ,KACf,KAAK,WAAW,KAAK,IAAMT,CAAE,IAAI,KAAK,IAAMC,CAAE,WAIrCS,EAAQjB,EAKd,GAAI,EAAE,KAAK,IAAIgB,EAAMH,EAAMC,EAAMC,CAAG,EAAIf,IAAY,CAAC7H,EACxD,KAAK,WAAW,KAAK,IAAMoI,CAAE,IAAI,KAAK,IAAMC,CAAE,OAI3C,CACH,IAAIU,EAAMT,EAAKE,EACXQ,EAAMT,EAAKE,EACXQ,EAAQP,EAAMA,EAAMC,EAAMA,EAC1BO,EAAQH,EAAMA,EAAMC,EAAMA,EAC1BG,GAAM,KAAK,KAAKF,CAAK,EACrBG,GAAM,KAAK,KAAKN,CAAK,EACrBpJ,GAAIM,EAAI,KAAK,KAAK2H,GAAK,KAAK,MAAMsB,EAAQH,EAAQI,IAAU,EAAIC,GAAMC,GAAI,GAAK,CAAC,EAChFC,EAAM3J,GAAI0J,GACVE,GAAM5J,GAAIyJ,GAGV,KAAK,IAAIE,EAAM,CAAC,EAAIxB,GACtB,KAAK,WAAWO,EAAKiB,EAAMT,CAAG,IAAIP,EAAKgB,EAAMR,CAAG,GAGlD,KAAK,WAAW7I,CAAC,IAAIA,CAAC,QAAQ,EAAE6I,EAAME,EAAMH,EAAMI,EAAI,IAAI,KAAK,IAAMZ,EAAKkB,GAAMZ,CAAG,IAAI,KAAK,IAAML,EAAKiB,GAAMX,CAAG,EACjH,CACF,CACD,IAAIlG,EAAGI,EAAG7C,EAAGuJ,EAAIC,EAAIC,EAAK,CAIxB,GAHAhH,EAAI,CAACA,EAAGI,EAAI,CAACA,EAAG7C,EAAI,CAACA,EAAGyJ,EAAM,CAAC,CAACA,EAG5BzJ,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAoBA,CAAC,EAAE,EAElD,IAAI0J,EAAK1J,EAAI,KAAK,IAAIuJ,CAAE,EACpBI,EAAK3J,EAAI,KAAK,IAAIuJ,CAAE,EACpBf,EAAK/F,EAAIiH,EACTjB,EAAK5F,EAAI8G,EACTC,EAAK,EAAIH,EACTI,EAAKJ,EAAMF,EAAKC,EAAKA,EAAKD,EAG1B,KAAK,MAAQ,KACf,KAAK,WAAWf,CAAE,IAAIC,CAAE,IAIjB,KAAK,IAAI,KAAK,IAAMD,CAAE,EAAIX,GAAW,KAAK,IAAI,KAAK,IAAMY,CAAE,EAAIZ,IACtE,KAAK,WAAWW,CAAE,IAAIC,CAAE,GAIrBzI,IAGD6J,EAAK,IAAGA,EAAKA,EAAKjC,GAAMA,IAGxBiC,EAAK/B,GACP,KAAK,WAAW9H,CAAC,IAAIA,CAAC,QAAQ4J,CAAE,IAAInH,EAAIiH,CAAE,IAAI7G,EAAI8G,CAAE,IAAI3J,CAAC,IAAIA,CAAC,QAAQ4J,CAAE,IAAI,KAAK,IAAMpB,CAAE,IAAI,KAAK,IAAMC,CAAE,GAInGoB,EAAKhC,GACZ,KAAK,WAAW7H,CAAC,IAAIA,CAAC,MAAM,EAAE6J,GAAMlC,GAAG,IAAIiC,CAAE,IAAI,KAAK,IAAMnH,EAAIzC,EAAI,KAAK,IAAIwJ,CAAE,CAAC,IAAI,KAAK,IAAM3G,EAAI7C,EAAI,KAAK,IAAIwJ,CAAE,CAAC,GAEtH,CACD,KAAK/G,EAAGI,EAAGiH,EAAG9I,EAAG,CACf,KAAK,WAAW,KAAK,IAAM,KAAK,IAAM,CAACyB,CAAC,IAAI,KAAK,IAAM,KAAK,IAAM,CAACI,CAAC,IAAIiH,EAAI,CAACA,CAAC,IAAI,CAAC9I,CAAC,IAAI,CAAC8I,CAAC,GAC3F,CACD,UAAW,CACT,OAAO,KAAK,CACb,CACH,CAEO,SAASC,IAAO,CACrB,OAAO,IAAI5B,EACb,CAGA4B,GAAK,UAAY5B,GAAK,UCvJP,SAAQpF,EAACN,EAAG,CACzB,OAAO,UAAoB,CACzB,OAAOA,CACX,CACA,CCJY,MAACuH,GAAM,KAAK,IACXC,GAAQ,KAAK,MACbC,GAAM,KAAK,IACX9I,GAAM,KAAK,IACXD,GAAM,KAAK,IACXgJ,GAAM,KAAK,IACXC,GAAO,KAAK,KAEZvC,GAAU,MACVF,GAAK,KAAK,GACV0C,GAAS1C,GAAK,EACdC,GAAM,EAAID,GAEhB,SAAS2C,GAAK7H,EAAG,CACtB,OAAOA,EAAI,EAAI,EAAIA,EAAI,GAAKkF,GAAK,KAAK,KAAKlF,CAAC,CAC9C,CAEO,SAAS8H,GAAK9H,EAAG,CACtB,OAAOA,GAAK,EAAI4H,GAAS5H,GAAK,GAAK,CAAC4H,GAAS,KAAK,KAAK5H,CAAC,CAC1D,CCjBO,SAAS+H,GAASC,EAAO,CAC9B,IAAIvC,EAAS,EAEb,OAAAuC,EAAM,OAAS,SAASC,EAAG,CACzB,GAAI,CAAC,UAAU,OAAQ,OAAOxC,EAC9B,GAAIwC,GAAK,KACPxC,EAAS,SACJ,CACL,MAAMvF,EAAI,KAAK,MAAM+H,CAAC,EACtB,GAAI,EAAE/H,GAAK,GAAI,MAAM,IAAI,WAAW,mBAAmB+H,CAAC,EAAE,EAC1DxC,EAASvF,CACV,CACD,OAAO8H,CACX,EAES,IAAM,IAAItC,GAAKD,CAAM,CAC9B,CChBe,SAAQyC,GAAClI,EAAG,CACzB,OAAO,OAAOA,GAAM,UAAY,WAAYA,EACxCA,EACA,MAAM,KAAKA,CAAC,CAClB,CCNA,SAASmI,GAAOC,EAAS,CACvB,KAAK,SAAWA,CAClB,CAEAD,GAAO,UAAY,CACjB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASnI,EAAGI,EAAG,CAEpB,OADAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAI,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAG,KACtC,CACF,CACH,EAEe,SAAQiI,GAACD,EAAS,CAC/B,OAAO,IAAID,GAAOC,CAAO,CAC3B,CC9BO,SAASpI,GAAEsI,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CAEO,SAASlI,GAAEkI,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CCAe,SAAAC,GAASvI,EAAGI,EAAG,CAC5B,IAAIoI,EAAUlI,EAAS,EAAI,EACvB8H,EAAU,KACVK,EAAQJ,GACRK,EAAS,KACTpB,EAAOS,GAASQ,CAAI,EAExBvI,EAAI,OAAOA,GAAM,WAAaA,EAAKA,IAAM,OAAa2I,GAASrI,EAASN,CAAC,EACzEI,EAAI,OAAOA,GAAM,WAAaA,EAAKA,IAAM,OAAawI,GAAStI,EAASF,CAAC,EAEzE,SAASmI,EAAKM,EAAM,CAClB,IAAIhJ,EACAvC,GAAKuL,EAAOX,GAAMW,CAAI,GAAG,OACzB3I,EACA4I,EAAW,GACXC,EAIJ,IAFIX,GAAW,OAAMM,EAASD,EAAMM,EAASzB,EAAI,CAAE,GAE9CzH,EAAI,EAAGA,GAAKvC,EAAG,EAAEuC,EAChB,EAAEA,EAAIvC,GAAKkL,EAAQtI,EAAI2I,EAAKhJ,CAAC,EAAGA,EAAGgJ,CAAI,KAAOC,KAC5CA,EAAW,CAACA,GAAUJ,EAAO,UAAS,EACrCA,EAAO,QAAO,GAEjBI,GAAUJ,EAAO,MAAM,CAAC1I,EAAEE,EAAGL,EAAGgJ,CAAI,EAAG,CAACzI,EAAEF,EAAGL,EAAGgJ,CAAI,CAAC,EAG3D,GAAIE,EAAQ,OAAOL,EAAS,KAAMK,EAAS,IAAM,IAClD,CAED,OAAAR,EAAK,EAAI,SAASN,EAAG,CACnB,OAAO,UAAU,QAAUjI,EAAI,OAAOiI,GAAM,WAAaA,EAAI3H,EAAS,CAAC2H,CAAC,EAAGM,GAAQvI,CACvF,EAEEuI,EAAK,EAAI,SAASN,EAAG,CACnB,OAAO,UAAU,QAAU7H,EAAI,OAAO6H,GAAM,WAAaA,EAAI3H,EAAS,CAAC2H,CAAC,EAAGM,GAAQnI,CACvF,EAEEmI,EAAK,QAAU,SAASN,EAAG,CACzB,OAAO,UAAU,QAAUO,EAAU,OAAOP,GAAM,WAAaA,EAAI3H,EAAS,CAAC,CAAC2H,CAAC,EAAGM,GAAQC,CAC9F,EAEED,EAAK,MAAQ,SAASN,EAAG,CACvB,OAAO,UAAU,QAAUQ,EAAQR,EAAGG,GAAW,OAASM,EAASD,EAAML,CAAO,GAAIG,GAAQE,CAChG,EAEEF,EAAK,QAAU,SAASN,EAAG,CACzB,OAAO,UAAU,QAAUA,GAAK,KAAOG,EAAUM,EAAS,KAAOA,EAASD,EAAML,EAAUH,CAAC,EAAGM,GAAQH,CAC1G,EAESG,CACT,CCzDe,SAAAS,GAAW,CAAA,CCAnB,SAASC,EAAMC,EAAMlJ,EAAGI,EAAG,CAChC8I,EAAK,SAAS,eACX,EAAIA,EAAK,IAAMA,EAAK,KAAO,GAC3B,EAAIA,EAAK,IAAMA,EAAK,KAAO,GAC3BA,EAAK,IAAM,EAAIA,EAAK,KAAO,GAC3BA,EAAK,IAAM,EAAIA,EAAK,KAAO,GAC3BA,EAAK,IAAM,EAAIA,EAAK,IAAMlJ,GAAK,GAC/BkJ,EAAK,IAAM,EAAIA,EAAK,IAAM9I,GAAK,CACpC,CACA,CAEO,SAAS+I,EAAMf,EAAS,CAC7B,KAAK,SAAWA,CAClB,CAEAe,EAAM,UAAY,CAChB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,IACtB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAGF,EAAM,KAAM,KAAK,IAAK,KAAK,GAAG,EACtC,IAAK,GAAG,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,KACnD,EACG,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASjJ,EAAGI,EAAG,CAEpB,OADAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAI,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,SAAS,QAAQ,EAAI,KAAK,IAAM,KAAK,KAAO,GAAI,EAAI,KAAK,IAAM,KAAK,KAAO,CAAC,EAC1G,QAAS6I,EAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EAChC,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACjC,CACH,EAEe,SAAQgJ,GAAChB,EAAS,CAC/B,OAAO,IAAIe,EAAMf,CAAO,CAC1B,CC/CA,SAASiB,GAAYjB,EAAS,CAC5B,KAAK,SAAWA,CAClB,CAEAiB,GAAY,UAAY,CACtB,UAAWL,EACX,QAASA,EACT,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IACjD,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACvD,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,SAAS,QAAQ,KAAK,IAAM,EAAI,KAAK,KAAO,GAAI,KAAK,IAAM,EAAI,KAAK,KAAO,CAAC,EACjF,KAAK,SAAS,QAAQ,KAAK,IAAM,EAAI,KAAK,KAAO,GAAI,KAAK,IAAM,EAAI,KAAK,KAAO,CAAC,EACjF,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KACD,CACF,CACF,EACD,MAAO,SAAShJ,EAAGI,EAAG,CAEpB,OADAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMJ,EAAG,KAAK,IAAMI,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMJ,EAAG,KAAK,IAAMI,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMJ,EAAG,KAAK,IAAMI,EAAG,KAAK,SAAS,QAAQ,KAAK,IAAM,EAAI,KAAK,IAAMJ,GAAK,GAAI,KAAK,IAAM,EAAI,KAAK,IAAMI,GAAK,CAAC,EAAG,MACjJ,QAAS6I,EAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EAChC,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACjC,CACH,EAEe,SAAQkJ,GAAClB,EAAS,CAC/B,OAAO,IAAIiB,GAAYjB,CAAO,CAChC,CCjDA,SAASmB,GAAUnB,EAAS,CAC1B,KAAK,SAAWA,CAClB,CAEAmB,GAAU,UAAY,CACpB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,IACtB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASvJ,EAAGI,EAAG,CAEpB,OADAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,IAAI2F,GAAM,KAAK,IAAM,EAAI,KAAK,IAAM/F,GAAK,EAAGgG,GAAM,KAAK,IAAM,EAAI,KAAK,IAAM5F,GAAK,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAO2F,EAAIC,CAAE,EAAI,KAAK,SAAS,OAAOD,EAAIC,CAAE,EAAG,MACvL,IAAK,GAAG,KAAK,OAAS,EACtB,QAASiD,EAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EAChC,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACjC,CACH,EAEe,SAAQoJ,GAACpB,EAAS,CAC/B,OAAO,IAAImB,GAAUnB,CAAO,CAC9B,CCpCA,SAASqB,GAAOrB,EAASsB,EAAM,CAC7B,KAAK,OAAS,IAAIP,EAAMf,CAAO,EAC/B,KAAK,MAAQsB,CACf,CAEAD,GAAO,UAAY,CACjB,UAAW,UAAW,CACpB,KAAK,GAAK,GACV,KAAK,GAAK,GACV,KAAK,OAAO,WACb,EACD,QAAS,UAAW,CAClB,IAAIzJ,EAAI,KAAK,GACTI,EAAI,KAAK,GACTuJ,EAAI3J,EAAE,OAAS,EAEnB,GAAI2J,EAAI,EAQN,QAPI5D,EAAK/F,EAAE,CAAC,EACRgG,EAAK5F,EAAE,CAAC,EACR6G,EAAKjH,EAAE2J,CAAC,EAAI5D,EACZmB,EAAK9G,EAAEuJ,CAAC,EAAI3D,EACZnG,EAAI,GACJD,EAEG,EAAEC,GAAK8J,GACZ/J,EAAIC,EAAI8J,EACR,KAAK,OAAO,MACV,KAAK,MAAQ3J,EAAEH,CAAC,GAAK,EAAI,KAAK,QAAUkG,EAAKnG,EAAIqH,GACjD,KAAK,MAAQ7G,EAAEP,CAAC,GAAK,EAAI,KAAK,QAAUmG,EAAKpG,EAAIsH,EAC3D,EAII,KAAK,GAAK,KAAK,GAAK,KACpB,KAAK,OAAO,SACb,EACD,MAAO,SAASlH,EAAGI,EAAG,CACpB,KAAK,GAAG,KAAK,CAACJ,CAAC,EACf,KAAK,GAAG,KAAK,CAACI,CAAC,CAChB,CACH,EAEA,MAAAwJ,GAAgB,SAASC,EAAOH,EAAM,CAEpC,SAASI,EAAO1B,EAAS,CACvB,OAAOsB,IAAS,EAAI,IAAIP,EAAMf,CAAO,EAAI,IAAIqB,GAAOrB,EAASsB,CAAI,CAClE,CAED,OAAAI,EAAO,KAAO,SAASJ,EAAM,CAC3B,OAAOG,EAAO,CAACH,CAAI,CACvB,EAESI,CACT,EAAG,GAAI,ECvDA,SAASb,EAAMC,EAAMlJ,EAAGI,EAAG,CAChC8I,EAAK,SAAS,cACZA,EAAK,IAAMA,EAAK,IAAMA,EAAK,IAAMA,EAAK,KACtCA,EAAK,IAAMA,EAAK,IAAMA,EAAK,IAAMA,EAAK,KACtCA,EAAK,IAAMA,EAAK,IAAMA,EAAK,IAAMlJ,GACjCkJ,EAAK,IAAMA,EAAK,IAAMA,EAAK,IAAM9I,GACjC8I,EAAK,IACLA,EAAK,GACT,CACA,CAEO,SAASa,GAAS3B,EAAS4B,EAAS,CACzC,KAAK,SAAW5B,EAChB,KAAK,IAAM,EAAI4B,GAAW,CAC5B,CAEAD,GAAS,UAAY,CACnB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAC3B,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACjC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAClD,IAAK,GAAGd,EAAM,KAAM,KAAK,IAAK,KAAK,GAAG,EAAG,KAC1C,EACG,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASjJ,EAAGI,EAAG,CAEpB,OADAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAI,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMJ,EAAG,KAAK,IAAMI,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS6I,EAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACtD,CACH,EAEA,MAAA6J,GAAgB,SAASJ,EAAOG,EAAS,CAEvC,SAASE,EAAS9B,EAAS,CACzB,OAAO,IAAI2B,GAAS3B,EAAS4B,CAAO,CACrC,CAED,OAAAE,EAAS,QAAU,SAASF,EAAS,CACnC,OAAOH,EAAO,CAACG,CAAO,CAC1B,EAESE,CACT,EAAG,CAAC,ECzDG,SAASC,GAAe/B,EAAS4B,EAAS,CAC/C,KAAK,SAAW5B,EAChB,KAAK,IAAM,EAAI4B,GAAW,CAC5B,CAEAG,GAAe,UAAY,CACzB,UAAWnB,EACX,QAASA,EACT,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAC5D,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IAClE,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KACD,CACF,CACF,EACD,MAAO,SAAShJ,EAAGI,EAAG,CAEpB,OADAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMJ,EAAG,KAAK,IAAMI,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,SAAS,OAAO,KAAK,IAAMJ,EAAG,KAAK,IAAMI,CAAC,EAAG,MAC3E,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMJ,EAAG,KAAK,IAAMI,EAAG,MACrD,QAAS6I,EAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACtD,CACH,EAEA,MAAAgK,GAAgB,SAASP,EAAOG,EAAS,CAEvC,SAASE,EAAS9B,EAAS,CACzB,OAAO,IAAI+B,GAAe/B,EAAS4B,CAAO,CAC3C,CAED,OAAAE,EAAS,QAAU,SAASF,EAAS,CACnC,OAAOH,EAAO,CAACG,CAAO,CAC1B,EAESE,CACT,EAAG,CAAC,EC1DG,SAASG,GAAajC,EAAS4B,EAAS,CAC7C,KAAK,SAAW5B,EAChB,KAAK,IAAM,EAAI4B,GAAW,CAC5B,CAEAK,GAAa,UAAY,CACvB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAC3B,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACjC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASrK,EAAGI,EAAG,CAEpB,OADAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAI,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAC3H,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS6I,EAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACtD,CACH,EAEA,MAAAkK,GAAgB,SAAST,EAAOG,EAAS,CAEvC,SAASE,EAAS9B,EAAS,CACzB,OAAO,IAAIiC,GAAajC,EAAS4B,CAAO,CACzC,CAED,OAAAE,EAAS,QAAU,SAASF,EAAS,CACnC,OAAOH,EAAO,CAACG,CAAO,CAC1B,EAESE,CACT,EAAG,CAAC,EC7CG,SAASjB,GAAMC,EAAMlJ,EAAGI,EAAG,CAChC,IAAIuF,EAAKuD,EAAK,IACVtD,EAAKsD,EAAK,IACVrD,EAAKqD,EAAK,IACVpD,EAAKoD,EAAK,IAEd,GAAIA,EAAK,OAAS9D,GAAS,CACzB,IAAI1H,EAAI,EAAIwL,EAAK,QAAU,EAAIA,EAAK,OAASA,EAAK,OAASA,EAAK,QAC5D5L,EAAI,EAAI4L,EAAK,QAAUA,EAAK,OAASA,EAAK,QAC9CvD,GAAMA,EAAKjI,EAAIwL,EAAK,IAAMA,EAAK,QAAUA,EAAK,IAAMA,EAAK,SAAW5L,EACpEsI,GAAMA,EAAKlI,EAAIwL,EAAK,IAAMA,EAAK,QAAUA,EAAK,IAAMA,EAAK,SAAW5L,CACrE,CAED,GAAI4L,EAAK,OAAS9D,GAAS,CACzB,IAAI3H,EAAI,EAAIyL,EAAK,QAAU,EAAIA,EAAK,OAASA,EAAK,OAASA,EAAK,QAC5DlM,EAAI,EAAIkM,EAAK,QAAUA,EAAK,OAASA,EAAK,QAC9CrD,GAAMA,EAAKpI,EAAIyL,EAAK,IAAMA,EAAK,QAAUlJ,EAAIkJ,EAAK,SAAWlM,EAC7D8I,GAAMA,EAAKrI,EAAIyL,EAAK,IAAMA,EAAK,QAAU9I,EAAI8I,EAAK,SAAWlM,CAC9D,CAEDkM,EAAK,SAAS,cAAcvD,EAAIC,EAAIC,EAAIC,EAAIoD,EAAK,IAAKA,EAAK,GAAG,CAChE,CAEA,SAASqB,GAAWnC,EAASoC,EAAO,CAClC,KAAK,SAAWpC,EAChB,KAAK,OAASoC,CAChB,CAEAD,GAAW,UAAY,CACrB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAC3B,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACjC,KAAK,OAAS,KAAK,OAAS,KAAK,OACjC,KAAK,QAAU,KAAK,QAAU,KAAK,QACnC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAClD,IAAK,GAAG,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAAG,KACzC,EACG,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASvK,EAAGI,EAAG,CAGpB,GAFAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EAET,KAAK,OAAQ,CACf,IAAIqK,EAAM,KAAK,IAAMzK,EACjB0K,EAAM,KAAK,IAAMtK,EACrB,KAAK,OAAS,KAAK,KAAK,KAAK,QAAU,KAAK,IAAIqK,EAAMA,EAAMC,EAAMA,EAAK,KAAK,MAAM,CAAC,CACpF,CAED,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAO1K,EAAGI,CAAC,EAAI,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS6I,GAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CAED,KAAK,OAAS,KAAK,OAAQ,KAAK,OAAS,KAAK,OAC9C,KAAK,QAAU,KAAK,QAAS,KAAK,QAAU,KAAK,QACjD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACtD,CACH,EAEA,MAAAuK,GAAgB,SAASd,EAAOW,EAAO,CAErC,SAASI,EAAWxC,EAAS,CAC3B,OAAOoC,EAAQ,IAAID,GAAWnC,EAASoC,CAAK,EAAI,IAAIT,GAAS3B,EAAS,CAAC,CACxE,CAED,OAAAwC,EAAW,MAAQ,SAASJ,EAAO,CACjC,OAAOX,EAAO,CAACW,CAAK,CACxB,EAESI,CACT,EAAG,EAAG,ECnFN,SAASC,GAAiBzC,EAASoC,EAAO,CACxC,KAAK,SAAWpC,EAChB,KAAK,OAASoC,CAChB,CAEAK,GAAiB,UAAY,CAC3B,UAAW7B,EACX,QAASA,EACT,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAC5D,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IAClE,KAAK,OAAS,KAAK,OAAS,KAAK,OACjC,KAAK,QAAU,KAAK,QAAU,KAAK,QACnC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KACD,CACF,CACF,EACD,MAAO,SAAShJ,EAAGI,EAAG,CAGpB,GAFAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EAET,KAAK,OAAQ,CACf,IAAIqK,EAAM,KAAK,IAAMzK,EACjB0K,EAAM,KAAK,IAAMtK,EACrB,KAAK,OAAS,KAAK,KAAK,KAAK,QAAU,KAAK,IAAIqK,EAAMA,EAAMC,EAAMA,EAAK,KAAK,MAAM,CAAC,CACpF,CAED,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAM1K,EAAG,KAAK,IAAMI,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,SAAS,OAAO,KAAK,IAAMJ,EAAG,KAAK,IAAMI,CAAC,EAAG,MAC3E,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMJ,EAAG,KAAK,IAAMI,EAAG,MACrD,QAAS6I,GAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CAED,KAAK,OAAS,KAAK,OAAQ,KAAK,OAAS,KAAK,OAC9C,KAAK,QAAU,KAAK,QAAS,KAAK,QAAU,KAAK,QACjD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACtD,CACH,EAEA,MAAA0K,GAAgB,SAASjB,EAAOW,EAAO,CAErC,SAASI,EAAWxC,EAAS,CAC3B,OAAOoC,EAAQ,IAAIK,GAAiBzC,EAASoC,CAAK,EAAI,IAAIL,GAAe/B,EAAS,CAAC,CACpF,CAED,OAAAwC,EAAW,MAAQ,SAASJ,EAAO,CACjC,OAAOX,EAAO,CAACW,CAAK,CACxB,EAESI,CACT,EAAG,EAAG,ECtEN,SAASG,GAAe3C,EAASoC,EAAO,CACtC,KAAK,SAAWpC,EAChB,KAAK,OAASoC,CAChB,CAEAO,GAAe,UAAY,CACzB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAC3B,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACjC,KAAK,OAAS,KAAK,OAAS,KAAK,OACjC,KAAK,QAAU,KAAK,QAAU,KAAK,QACnC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAAS/K,EAAGI,EAAG,CAGpB,GAFAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EAET,KAAK,OAAQ,CACf,IAAIqK,EAAM,KAAK,IAAMzK,EACjB0K,EAAM,KAAK,IAAMtK,EACrB,KAAK,OAAS,KAAK,KAAK,KAAK,QAAU,KAAK,IAAIqK,EAAMA,EAAMC,EAAMA,EAAK,KAAK,MAAM,CAAC,CACpF,CAED,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAI,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAC3H,IAAK,GAAG,KAAK,OAAS,EACtB,QAASzB,GAAM,KAAMjJ,EAAGI,CAAC,EAAG,KAC7B,CAED,KAAK,OAAS,KAAK,OAAQ,KAAK,OAAS,KAAK,OAC9C,KAAK,QAAU,KAAK,QAAS,KAAK,QAAU,KAAK,QACjD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,CACtD,CACH,EAEA,MAAA4K,GAAgB,SAASnB,EAAOW,EAAO,CAErC,SAASI,EAAWxC,EAAS,CAC3B,OAAOoC,EAAQ,IAAIO,GAAe3C,EAASoC,CAAK,EAAI,IAAIH,GAAajC,EAAS,CAAC,CAChF,CAED,OAAAwC,EAAW,MAAQ,SAASJ,EAAO,CACjC,OAAOX,EAAO,CAACW,CAAK,CACxB,EAESI,CACT,EAAG,EAAG,EC3DN,SAASK,GAAa7C,EAAS,CAC7B,KAAK,SAAWA,CAClB,CAEA6C,GAAa,UAAY,CACvB,UAAWjC,EACX,QAASA,EACT,UAAW,UAAW,CACpB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CACd,KAAK,QAAQ,KAAK,SAAS,UAAS,CACzC,EACD,MAAO,SAAShJ,EAAGI,EAAG,CACpBJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACT,KAAK,OAAQ,KAAK,SAAS,OAAOJ,EAAGI,CAAC,GACrC,KAAK,OAAS,EAAG,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAChD,CACH,EAEe,SAAQ8K,GAAC9C,EAAS,CAC/B,OAAO,IAAI6C,GAAa7C,CAAO,CACjC,CCxBA,SAAS+C,GAAKnL,EAAG,CACf,OAAOA,EAAI,EAAI,GAAK,CACtB,CAMA,SAASoL,GAAOlC,EAAMrD,EAAIC,EAAI,CAC5B,IAAIuF,EAAKnC,EAAK,IAAMA,EAAK,IACrBoC,EAAKzF,EAAKqD,EAAK,IACfqC,GAAMrC,EAAK,IAAMA,EAAK,MAAQmC,GAAMC,EAAK,GAAK,IAC9CE,GAAM1F,EAAKoD,EAAK,MAAQoC,GAAMD,EAAK,GAAK,IACxC/C,GAAKiD,EAAKD,EAAKE,EAAKH,IAAOA,EAAKC,GACpC,OAAQH,GAAKI,CAAE,EAAIJ,GAAKK,CAAE,GAAK,KAAK,IAAI,KAAK,IAAID,CAAE,EAAG,KAAK,IAAIC,CAAE,EAAG,GAAM,KAAK,IAAIlD,CAAC,CAAC,GAAK,CAC5F,CAGA,SAASmD,GAAOvC,EAAMtJ,EAAG,CACvB,IAAIrB,EAAI2K,EAAK,IAAMA,EAAK,IACxB,OAAO3K,GAAK,GAAK2K,EAAK,IAAMA,EAAK,KAAO3K,EAAIqB,GAAK,EAAIA,CACvD,CAKA,SAASqJ,EAAMC,EAAMjE,EAAI9F,EAAI,CAC3B,IAAI4G,EAAKmD,EAAK,IACVlD,EAAKkD,EAAK,IACVvD,EAAKuD,EAAK,IACVtD,EAAKsD,EAAK,IACVjC,GAAMtB,EAAKI,GAAM,EACrBmD,EAAK,SAAS,cAAcnD,EAAKkB,EAAIjB,EAAKiB,EAAKhC,EAAIU,EAAKsB,EAAIrB,EAAKqB,EAAK9H,EAAIwG,EAAIC,CAAE,CAClF,CAEA,SAAS8F,EAAUtD,EAAS,CAC1B,KAAK,SAAWA,CAClB,CAEAsD,EAAU,UAAY,CACpB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,IACX,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAClD,IAAK,GAAGzC,EAAM,KAAM,KAAK,IAAKwC,GAAO,KAAM,KAAK,GAAG,CAAC,EAAG,KACxD,EACG,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASzL,EAAGI,EAAG,CACpB,IAAIjB,EAAK,IAGT,GADAa,EAAI,CAACA,EAAGI,EAAI,CAACA,EACT,EAAAJ,IAAM,KAAK,KAAOI,IAAM,KAAK,KACjC,QAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAI,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG6I,EAAM,KAAMwC,GAAO,KAAMtM,EAAKiM,GAAO,KAAMpL,EAAGI,CAAC,CAAC,EAAGjB,CAAE,EAAG,MACjF,QAAS8J,EAAM,KAAM,KAAK,IAAK9J,EAAKiM,GAAO,KAAMpL,EAAGI,CAAC,CAAC,EAAG,KAC1D,CAED,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMJ,EAChC,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMI,EAChC,KAAK,IAAMjB,EACZ,CACH,EAEA,SAASwM,GAAUvD,EAAS,CAC1B,KAAK,SAAW,IAAIwD,GAAexD,CAAO,CAC5C,EAECuD,GAAU,UAAY,OAAO,OAAOD,EAAU,SAAS,GAAG,MAAQ,SAAS1L,EAAGI,EAAG,CAChFsL,EAAU,UAAU,MAAM,KAAK,KAAMtL,EAAGJ,CAAC,CAC3C,EAEA,SAAS4L,GAAexD,EAAS,CAC/B,KAAK,SAAWA,CAClB,CAEAwD,GAAe,UAAY,CACzB,OAAQ,SAAS5L,EAAGI,EAAG,CAAE,KAAK,SAAS,OAAOA,EAAGJ,CAAC,CAAI,EACtD,UAAW,UAAW,CAAE,KAAK,SAAS,UAAW,CAAG,EACpD,OAAQ,SAASA,EAAGI,EAAG,CAAE,KAAK,SAAS,OAAOA,EAAGJ,CAAC,CAAI,EACtD,cAAe,SAAS2F,EAAIC,EAAIC,EAAIC,EAAI9F,EAAGI,EAAG,CAAE,KAAK,SAAS,cAAcwF,EAAID,EAAIG,EAAID,EAAIzF,EAAGJ,CAAC,CAAI,CACtG,EAEO,SAAS6L,GAAUzD,EAAS,CACjC,OAAO,IAAIsD,EAAUtD,CAAO,CAC9B,CAEO,SAAS0D,GAAU1D,EAAS,CACjC,OAAO,IAAIuD,GAAUvD,CAAO,CAC9B,CCvGA,SAAS2D,GAAQ3D,EAAS,CACxB,KAAK,SAAWA,CAClB,CAEA2D,GAAQ,UAAY,CAClB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,GAAK,GACV,KAAK,GAAK,EACX,EACD,QAAS,UAAW,CAClB,IAAI/L,EAAI,KAAK,GACTI,EAAI,KAAK,GACT,EAAIJ,EAAE,OAEV,GAAI,EAEF,GADA,KAAK,MAAQ,KAAK,SAAS,OAAOA,EAAE,CAAC,EAAGI,EAAE,CAAC,CAAC,EAAI,KAAK,SAAS,OAAOJ,EAAE,CAAC,EAAGI,EAAE,CAAC,CAAC,EAC3E,IAAM,EACR,KAAK,SAAS,OAAOJ,EAAE,CAAC,EAAGI,EAAE,CAAC,CAAC,MAI/B,SAFI4L,EAAKC,GAAcjM,CAAC,EACpBkM,EAAKD,GAAc7L,CAAC,EACf+L,EAAK,EAAGC,EAAK,EAAGA,EAAK,EAAG,EAAED,EAAI,EAAEC,EACvC,KAAK,SAAS,cAAcJ,EAAG,CAAC,EAAEG,CAAE,EAAGD,EAAG,CAAC,EAAEC,CAAE,EAAGH,EAAG,CAAC,EAAEG,CAAE,EAAGD,EAAG,CAAC,EAAEC,CAAE,EAAGnM,EAAEoM,CAAE,EAAGhM,EAAEgM,CAAE,CAAC,GAKtF,KAAK,OAAU,KAAK,QAAU,GAAK,IAAM,IAAI,KAAK,SAAS,UAAS,EACxE,KAAK,MAAQ,EAAI,KAAK,MACtB,KAAK,GAAK,KAAK,GAAK,IACrB,EACD,MAAO,SAASpM,EAAGI,EAAG,CACpB,KAAK,GAAG,KAAK,CAACJ,CAAC,EACf,KAAK,GAAG,KAAK,CAACI,CAAC,CAChB,CACH,EAGA,SAAS6L,GAAcjM,EAAG,CACxB,IAAI,EACA,EAAIA,EAAE,OAAS,EACfhD,EACAU,EAAI,IAAI,MAAM,CAAC,EACfD,EAAI,IAAI,MAAM,CAAC,EACfF,EAAI,IAAI,MAAM,CAAC,EAEnB,IADAG,EAAE,CAAC,EAAI,EAAGD,EAAE,CAAC,EAAI,EAAGF,EAAE,CAAC,EAAIyC,EAAE,CAAC,EAAI,EAAIA,EAAE,CAAC,EACpC,EAAI,EAAG,EAAI,EAAI,EAAG,EAAE,EAAGtC,EAAE,CAAC,EAAI,EAAGD,EAAE,CAAC,EAAI,EAAGF,EAAE,CAAC,EAAI,EAAIyC,EAAE,CAAC,EAAI,EAAIA,EAAE,EAAI,CAAC,EAE7E,IADAtC,EAAE,EAAI,CAAC,EAAI,EAAGD,EAAE,EAAI,CAAC,EAAI,EAAGF,EAAE,EAAI,CAAC,EAAI,EAAIyC,EAAE,EAAI,CAAC,EAAIA,EAAE,CAAC,EACpD,EAAI,EAAG,EAAI,EAAG,EAAE,EAAGhD,EAAIU,EAAE,CAAC,EAAID,EAAE,EAAI,CAAC,EAAGA,EAAE,CAAC,GAAKT,EAAGO,EAAE,CAAC,GAAKP,EAAIO,EAAE,EAAI,CAAC,EAE3E,IADAG,EAAE,EAAI,CAAC,EAAIH,EAAE,EAAI,CAAC,EAAIE,EAAE,EAAI,CAAC,EACxB,EAAI,EAAI,EAAG,GAAK,EAAG,EAAE,EAAGC,EAAE,CAAC,GAAKH,EAAE,CAAC,EAAIG,EAAE,EAAI,CAAC,GAAKD,EAAE,CAAC,EAE3D,IADAA,EAAE,EAAI,CAAC,GAAKuC,EAAE,CAAC,EAAItC,EAAE,EAAI,CAAC,GAAK,EAC1B,EAAI,EAAG,EAAI,EAAI,EAAG,EAAE,EAAGD,EAAE,CAAC,EAAI,EAAIuC,EAAE,EAAI,CAAC,EAAItC,EAAE,EAAI,CAAC,EACzD,MAAO,CAACA,EAAGD,CAAC,CACd,CAEe,SAAQ4O,GAACjE,EAAS,CAC/B,OAAO,IAAI2D,GAAQ3D,CAAO,CAC5B,CChEA,SAASkE,EAAKlE,EAASxI,EAAG,CACxB,KAAK,SAAWwI,EAChB,KAAK,GAAKxI,CACZ,CAEA0M,EAAK,UAAY,CACf,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,GAAK,KAAK,GAAK,IACpB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CACd,EAAI,KAAK,IAAM,KAAK,GAAK,GAAK,KAAK,SAAW,GAAG,KAAK,SAAS,OAAO,KAAK,GAAI,KAAK,EAAE,GACtF,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAC9E,KAAK,OAAS,IAAG,KAAK,GAAK,EAAI,KAAK,GAAI,KAAK,MAAQ,EAAI,KAAK,MACnE,EACD,MAAO,SAAStM,EAAGI,EAAG,CAEpB,OADAJ,EAAI,CAACA,EAAGI,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAI,KAAK,SAAS,OAAOJ,EAAGI,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS,CACP,GAAI,KAAK,IAAM,EACb,KAAK,SAAS,OAAO,KAAK,GAAIA,CAAC,EAC/B,KAAK,SAAS,OAAOJ,EAAGI,CAAC,MACpB,CACL,IAAIuF,EAAK,KAAK,IAAM,EAAI,KAAK,IAAM3F,EAAI,KAAK,GAC5C,KAAK,SAAS,OAAO2F,EAAI,KAAK,EAAE,EAChC,KAAK,SAAS,OAAOA,EAAIvF,CAAC,CAC3B,CACD,KACD,CACF,CACD,KAAK,GAAKJ,EAAG,KAAK,GAAKI,CACxB,CACH,EAEe,SAAQmM,GAACnE,EAAS,CAC/B,OAAO,IAAIkE,EAAKlE,EAAS,EAAG,CAC9B,CAEO,SAASoE,GAAWpE,EAAS,CAClC,OAAO,IAAIkE,EAAKlE,EAAS,CAAC,CAC5B,CAEO,SAASqE,GAAUrE,EAAS,CACjC,OAAO,IAAIkE,EAAKlE,EAAS,CAAC,CAC5B", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]}