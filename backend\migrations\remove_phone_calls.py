"""
Migration script to remove phone calls feature from database
Run this script to remove phone_calls_limit and phone_calls_used columns from subscriptions table
"""
import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
import logging

# Add the parent directory to the path so we can import from backend
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.models.database import DATABASE_URL

logger = logging.getLogger(__name__)

def remove_phone_calls_columns():
    """Remove phone_calls_limit and phone_calls_used columns from subscriptions table"""
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as connection:
            # Check if columns exist before trying to drop them
            if "sqlite" in DATABASE_URL.lower():
                # For SQLite, we need to check if columns exist first
                result = connection.execute(text("PRAGMA table_info(subscriptions)"))
                columns = [row[1] for row in result.fetchall()]
                
                if 'phone_calls_limit' in columns or 'phone_calls_used' in columns:
                    print("⚠️  SQLite detected. Phone calls columns exist but cannot be dropped directly.")
                    print("   SQLite doesn't support DROP COLUMN. The columns will be ignored by the new model.")
                    print("   If you need to completely remove them, you would need to recreate the table.")
                else:
                    print("✅ Phone calls columns not found in subscriptions table.")
                    
            else:
                # For PostgreSQL/MySQL, we can drop columns directly
                try:
                    connection.execute(text("ALTER TABLE subscriptions DROP COLUMN IF EXISTS phone_calls_limit"))
                    print("✅ Removed phone_calls_limit column")
                except OperationalError as e:
                    print(f"ℹ️  phone_calls_limit column may not exist: {e}")
                
                try:
                    connection.execute(text("ALTER TABLE subscriptions DROP COLUMN IF EXISTS phone_calls_used"))
                    print("✅ Removed phone_calls_used column")
                except OperationalError as e:
                    print(f"ℹ️  phone_calls_used column may not exist: {e}")
                
                connection.commit()
                
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        return False
    
    return True

def main():
    """Run the migration"""
    print("🔄 Starting phone calls feature removal migration...")
    
    if remove_phone_calls_columns():
        print("✅ Migration completed successfully!")
        print("📝 Note: The application will now ignore any phone calls references in the database.")
    else:
        print("❌ Migration failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
