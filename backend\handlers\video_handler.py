"""
Gemini Video WebRTC handler for AI Therapist
"""
import asyncio
import time
import logging
import numpy as np
from google import genai
from google.genai.types import (
    LiveConnectConfig,
    PrebuiltVoiceConfig,
    SpeechConfig,
    VoiceConfig,
)

from backend.config.settings import get_settings, SYSTEM_PROMPT
from backend.services.webrtc_service import AsyncAudioVideoStreamHandler, FASTRTC_AVAILABLE, wait_for_item
from backend.utils.audio_utils import encode_audio_dict, encode_image, detect_goodbye
from backend.utils.session_manager import add_session, remove_session, get_session_user
from backend.services.memory_service import memory_service
from backend.models.database import get_db

logger = logging.getLogger(__name__)

class GeminiVideoHandler(AsyncAudioVideoStreamHandler):
    """Enhanced handler for Gemini API with audio and video capabilities for therapeutic sessions"""

    def __init__(self) -> None:
        super().__init__(
            expected_layout="mono",
            output_sample_rate=24000,
            input_sample_rate=16000,
        )
        self.audio_queue = asyncio.Queue()
        self.session = None
        self.last_frame_time = 0
        self.quit = asyncio.Event()
        self.session_id = None
        self.uploaded_files = []  # Store uploaded files
        self.settings = get_settings()
        self.phone_mode = self.settings.mode == "PHONE"
        self.current_voice = "Aoede"  # Track current voice
        self.session_created = False
        self.client = None
        logger.info("🎥 VIDEO HANDLER: __init__ called - Video handler created!")

    def copy(self) -> "GeminiVideoHandler":
        return GeminiVideoHandler()

    def set_args(self, args):
        """Override set_args to handle voice and file updates"""
        super().set_args(args)

        # Update voice if provided (for logging purposes only)
        if args and len(args) > 1:
            new_voice = args[1]
            if new_voice != self.current_voice:
                logger.info(f"🎯 VIDEO set_args: Voice updated from {self.current_voice} to {new_voice} (session already created)")
                self.current_voice = new_voice

        # Check for uploaded files
        if args and len(args) > 2 and args[2]:
            self.uploaded_files = args[2]

    async def start_up(self):
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping video handler setup")
            return

        try:
            # EXACT SAME LOGIC AS VOICE HANDLER (which works perfectly)
            # Set default voice
            voice_name = "Aoede"

            # Try to get voice from args if available - Wait for FINAL voice selection
            if not self.phone_mode:
                try:
                    # Wait for first voice selection
                    await asyncio.wait_for(self.wait_for_args(), timeout=3.0)
                    if self.latest_args and len(self.latest_args) > 1:
                        voice_name = self.latest_args[1]
                        logger.info(f"Received FIRST voice selection from frontend: {voice_name}")

                        # If it's the default Aoede, wait for user's actual selection
                        if voice_name == "Aoede":
                            logger.info("🎯 Received default Aoede, waiting for user's voice selection...")
                            try:
                                # Wait up to 5 more seconds for user's actual voice selection
                                await asyncio.wait_for(self.wait_for_args(), timeout=5.0)
                                if self.latest_args and len(self.latest_args) > 1:
                                    new_voice = self.latest_args[1]
                                    if new_voice != voice_name:
                                        voice_name = new_voice
                                        logger.info(f"✅ Received USER voice selection: {voice_name}")
                                    else:
                                        logger.info(f"User confirmed default voice: {voice_name}")
                            except (asyncio.TimeoutError, Exception):
                                logger.info(f"No user voice selection received, using: {voice_name}")
                        else:
                            logger.info(f"✅ Received non-default voice: {voice_name}")
                except (asyncio.TimeoutError, Exception) as args_error:
                    logger.warning(f"Could not get args, using default voice: {args_error}")

            logger.info(f"Starting video handler in {'phone' if self.phone_mode else 'normal'} mode with voice: {voice_name}")
            self.current_voice = voice_name

            self.client = genai.Client(
                api_key=self.settings.gemini_api_key,
                http_options={"api_version": "v1alpha"}
            )

            # Create session with selected voice
            await self._create_session_with_voice(voice_name)


                        
        except Exception as e:
            logger.error(f"Error in GeminiVideoHandler start_up: {e}")

    async def _create_session_with_voice(self, voice_name: str):
        """Create a new Gemini Live session with the specified voice"""
        if self.session_created:
            logger.info(f"Session already created, ignoring voice change to {voice_name}")
            return

        try:
            logger.info(f"Creating Gemini Video Live session with voice: {voice_name}")

            # Create proper typed configuration for voice
            try:
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=voice_name,
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.8,
                        "max_output_tokens": 256,
                    },
                    system_instruction={
                        "parts": [{"text": SYSTEM_PROMPT}]
                    }
                )
                logger.info(f"✅ VIDEO LiveConnectConfig created with VOICE: {voice_name}")
                logger.info(f"🎥🎤 Video voice configuration: {voice_name} (should be different from Aoede if selected)")
            except Exception as config_error:
                logger.warning(f"Failed to create config with system_instruction: {config_error}")
                # Fallback config without system_instruction
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=voice_name,
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.8,
                        "max_output_tokens": 256,
                    }
                )
                logger.info(f"✅ VIDEO Fallback LiveConnectConfig created with VOICE: {voice_name}")
                logger.info(f"🎥🎤 Video fallback voice configuration: {voice_name}")

            async with self.client.aio.live.connect(
                model="gemini-2.0-flash-exp",
                config=config
            ) as session:
                self.session = session
                self.session_id = f"video_{int(time.time())}"
                self.session_created = True
                add_session(self.session_id, "video", self)
                logger.info(f"Gemini Video Live session established with voice: {voice_name}")

                # Send identity reinforcement
                try:
                    identity_msg = (
                        f"IMPORTANT: You are an AI Therapist created by Critical Future. "
                        f"Your voice name is {voice_name}. You can see the person you're helping. "
                        f"Use their visual cues to provide better therapeutic support. "
                        f"Never say you are created by Google or Gemini. Always say Critical Future."
                    )
                    await session.send(identity_msg)

                    # Add user memory context if available
                    user_id, user_name = get_session_user(self.session_id)
                    if user_id:
                        try:
                            db = next(get_db())
                            user_context = memory_service.get_user_context(user_id, db)
                            if user_context:
                                context_msg = f"USER MEMORY CONTEXT:\n{user_context}"
                                await session.send(context_msg)
                                logger.info(f"User memory context sent for user {user_id}")
                            db.close()
                        except Exception as memory_error:
                            logger.warning(f"Could not load user memory: {memory_error}")

                    # Send uploaded files if any
                    if self.uploaded_files:
                        for file_data in self.uploaded_files:
                            await session.send(file_data)
                            logger.info("Uploaded file sent to AI")

                    logger.info("Video session identity reinforcement sent")
                except Exception as prompt_error:
                    logger.warning(f"Could not send video session setup: {prompt_error}")

                # Keep session alive and process responses
                logger.info("Starting video session response processing loop")
                try:
                    while not self.quit.is_set():
                        try:
                            # Process incoming responses from Gemini (essential for keeping session alive)
                            logger.debug("Waiting for video session responses...")
                            turn = session.receive()
                            async for response in turn:
                                if self.quit.is_set():
                                    logger.info("Quit signal received during video response processing")
                                    break

                                # Handle audio data from Gemini
                                if hasattr(response, 'data') and response.data and len(response.data) > 0:
                                    try:
                                        array = np.frombuffer(response.data, dtype=np.int16).reshape(1, -1)
                                        if not self.quit.is_set() and array is not None and array.size > 0:
                                            try:
                                                self.audio_queue.put_nowait(array)
                                                logger.debug(f"Added video audio chunk to queue: {array.shape}")
                                            except asyncio.QueueFull:
                                                logger.warning("Video audio queue full, dropping audio frame")
                                    except Exception as audio_error:
                                        logger.error(f"Error processing video audio chunk: {audio_error}")

                                # Handle text responses from Gemini
                                if hasattr(response, 'text') and response.text:
                                    logger.info(f"Gemini video text response: {response.text[:100]}...")

                                    # Check for goodbye and auto-end session
                                    if "goodbye" in response.text.lower() or "bye" in response.text.lower():
                                        logger.info("Goodbye detected in video session, ending")
                                        await asyncio.sleep(2)  # Let the goodbye message play
                                        self.quit.set()
                                        break

                                    # Check for incorrect identity and send correction
                                    if any(phrase in response.text.lower() for phrase in ["i am gemini", "created by google", "i'm gemini", "google ai"]):
                                        correction_msg = (
                                            "CORRECTION: Remember, you are an AI Therapist created by Critical Future, "
                                            f"not by Google or Gemini. Your voice name is {voice_name}. Please correct your response."
                                        )
                                        try:
                                            await session.send(correction_msg)
                                            logger.info("Sent video identity correction")
                                        except Exception as correction_error:
                                            logger.warning(f"Could not send video correction: {correction_error}")
                        except Exception as turn_error:
                            if "1000" in str(turn_error) or "1001" in str(turn_error):  # Normal WebSocket close
                                logger.info("Video session ended normally")
                                break
                            elif "1011" in str(turn_error):  # Keepalive timeout
                                logger.warning("Video session keepalive timeout, continuing...")
                                continue
                            else:
                                logger.error(f"Error in video session turn: {turn_error}")
                                await asyncio.sleep(0.1)  # Brief pause before retrying

                except Exception as e:
                    logger.error(f"Error in video session processing: {e}")

                logger.info("Video session processing completed")

        except Exception as e:
            logger.error(f"Error creating video session with voice {voice_name}: {e}")
        finally:
            # Clean up session
            if self.session_id:
                remove_session(self.session_id)

    async def video_receive(self, frame: np.ndarray):
        """Receive video frame from client"""
        if self.session and frame is not None and frame.size > 0:
            # Send image every 2 seconds to avoid overwhelming the API
            if time.time() - self.last_frame_time > 2:
                self.last_frame_time = time.time()
                try:
                    await self.session.send(encode_image(frame))
                except Exception as e:
                    logger.warning(f"Error sending video frame: {e}")

    async def video_emit(self):
        """Video emit - AI doesn't send video back, only audio"""
        # Return None since AI doesn't send video back to user
        # FastRTC requires this method for send-receive mode
        return None

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from client"""
        try:
            if frame is None or len(frame) != 2:
                return

            _, array = frame
            if array is None:
                return

            array = array.squeeze()
            if array is None or array.size == 0:
                return

            audio_message = encode_audio_dict(array)
            if self.session and audio_message:
                await self.session.send(audio_message)
        except Exception as e:
            logger.error(f"Error processing audio in video session: {e}")

    async def emit(self):
        """Emit audio response to client"""
        try:
            array = await wait_for_item(self.audio_queue, 0.01)
            if array is not None:
                return (self.output_sample_rate, array)
            return None
        except Exception as e:
            logger.error(f"Error emitting audio in video session: {e}")
            return None

    async def shutdown(self) -> None:
        """Clean shutdown of video handler"""
        logger.info("Shutting down Gemini video handler")
        if self.session:
            self.quit.set()
            try:
                await self.session.close()
            except Exception as e:
                logger.warning(f"Error closing video session: {e}")
            self.quit.clear()
            
        # Clean up session
        if self.session_id:
            remove_session(self.session_id)
