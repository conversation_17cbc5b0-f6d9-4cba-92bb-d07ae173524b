{"version": 3, "file": "thinInstanceMesh-BkxjteXz.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Meshes/thinInstanceMesh.js"], "sourcesContent": ["import { Mesh } from \"../Meshes/mesh.js\";\nimport { Vert<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from \"../Buffers/buffer.js\";\nimport { Matrix, Vector3, TmpVectors } from \"../Maths/math.vector.js\";\nimport { Logger } from \"../Misc/logger.js\";\nimport { BoundingInfo } from \"../Culling/boundingInfo.js\";\nMesh.prototype.thinInstanceAdd = function (matrix, refresh = true) {\n    if (!this.getScene().getEngine().getCaps().instancedArrays) {\n        Logger.Error(\"Thin Instances are not supported on this device as Instanced Array extension not supported\");\n        return -1;\n    }\n    this._thinInstanceUpdateBufferSize(\"matrix\", Array.isArray(matrix) ? matrix.length : 1);\n    const index = this._thinInstanceDataStorage.instancesCount;\n    if (Array.isArray(matrix)) {\n        for (let i = 0; i < matrix.length; ++i) {\n            this.thinInstanceSetMatrixAt(this._thinInstanceDataStorage.instancesCount++, matrix[i], i === matrix.length - 1 && refresh);\n        }\n    }\n    else {\n        this.thinInstanceSetMatrixAt(this._thinInstanceDataStorage.instancesCount++, matrix, refresh);\n    }\n    return index;\n};\nMesh.prototype.thinInstanceAddSelf = function (refresh = true) {\n    return this.thinInstanceAdd(Matrix.IdentityReadOnly, refresh);\n};\nMesh.prototype.thinInstanceRegisterAttribute = function (kind, stride) {\n    // preserve backward compatibility\n    if (kind === VertexBuffer.ColorKind) {\n        kind = VertexBuffer.ColorInstanceKind;\n    }\n    this.removeVerticesData(kind);\n    this._thinInstanceInitializeUserStorage();\n    this._userThinInstanceBuffersStorage.strides[kind] = stride;\n    this._userThinInstanceBuffersStorage.sizes[kind] = stride * Math.max(32, this._thinInstanceDataStorage.instancesCount); // Initial size\n    this._userThinInstanceBuffersStorage.data[kind] = new Float32Array(this._userThinInstanceBuffersStorage.sizes[kind]);\n    this._userThinInstanceBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), this._userThinInstanceBuffersStorage.data[kind], kind, true, false, stride, true);\n    this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[kind]);\n};\nMesh.prototype.thinInstanceSetMatrixAt = function (index, matrix, refresh = true) {\n    if (!this._thinInstanceDataStorage.matrixData || index >= this._thinInstanceDataStorage.instancesCount) {\n        return false;\n    }\n    const matrixData = this._thinInstanceDataStorage.matrixData;\n    matrix.copyToArray(matrixData, index * 16);\n    if (this._thinInstanceDataStorage.worldMatrices) {\n        this._thinInstanceDataStorage.worldMatrices[index] = matrix;\n    }\n    if (refresh) {\n        this.thinInstanceBufferUpdated(\"matrix\");\n        if (!this.doNotSyncBoundingInfo) {\n            this.thinInstanceRefreshBoundingInfo(false);\n        }\n    }\n    return true;\n};\nMesh.prototype.thinInstanceSetAttributeAt = function (kind, index, value, refresh = true) {\n    // preserve backward compatibility\n    if (kind === VertexBuffer.ColorKind) {\n        kind = VertexBuffer.ColorInstanceKind;\n    }\n    if (!this._userThinInstanceBuffersStorage || !this._userThinInstanceBuffersStorage.data[kind] || index >= this._thinInstanceDataStorage.instancesCount) {\n        return false;\n    }\n    this._thinInstanceUpdateBufferSize(kind, 0); // make sur the buffer for the kind attribute is big enough\n    this._userThinInstanceBuffersStorage.data[kind].set(value, index * this._userThinInstanceBuffersStorage.strides[kind]);\n    if (refresh) {\n        this.thinInstanceBufferUpdated(kind);\n    }\n    return true;\n};\nObject.defineProperty(Mesh.prototype, \"thinInstanceCount\", {\n    get: function () {\n        return this._thinInstanceDataStorage.instancesCount;\n    },\n    set: function (value) {\n        const matrixData = this._thinInstanceDataStorage.matrixData ?? this.source?._thinInstanceDataStorage.matrixData;\n        const numMaxInstances = matrixData ? matrixData.length / 16 : 0;\n        if (value <= numMaxInstances) {\n            this._thinInstanceDataStorage.instancesCount = value;\n        }\n    },\n    enumerable: true,\n    configurable: true,\n});\nMesh.prototype._thinInstanceCreateMatrixBuffer = function (kind, buffer, staticBuffer = true) {\n    const matrixBuffer = new Buffer(this.getEngine(), buffer, !staticBuffer, 16, false, true);\n    for (let i = 0; i < 4; i++) {\n        this.setVerticesBuffer(matrixBuffer.createVertexBuffer(kind + i, i * 4, 4));\n    }\n    return matrixBuffer;\n};\nMesh.prototype.thinInstanceSetBuffer = function (kind, buffer, stride = 0, staticBuffer = true) {\n    stride = stride || 16;\n    if (kind === \"matrix\") {\n        this._thinInstanceDataStorage.matrixBuffer?.dispose();\n        this._thinInstanceDataStorage.matrixBuffer = null;\n        this._thinInstanceDataStorage.matrixBufferSize = buffer ? buffer.length : 32 * stride;\n        this._thinInstanceDataStorage.matrixData = buffer;\n        this._thinInstanceDataStorage.worldMatrices = null;\n        if (buffer !== null) {\n            this._thinInstanceDataStorage.instancesCount = buffer.length / stride;\n            this._thinInstanceDataStorage.matrixBuffer = this._thinInstanceCreateMatrixBuffer(\"world\", buffer, staticBuffer);\n            if (!this.doNotSyncBoundingInfo) {\n                this.thinInstanceRefreshBoundingInfo(false);\n            }\n        }\n        else {\n            this._thinInstanceDataStorage.instancesCount = 0;\n            if (!this.doNotSyncBoundingInfo) {\n                // mesh has no more thin instances, so need to recompute the bounding box because it's the regular mesh that will now be displayed\n                this.refreshBoundingInfo();\n            }\n        }\n    }\n    else if (kind === \"previousMatrix\") {\n        this._thinInstanceDataStorage.previousMatrixBuffer?.dispose();\n        this._thinInstanceDataStorage.previousMatrixBuffer = null;\n        this._thinInstanceDataStorage.previousMatrixData = buffer;\n        if (buffer !== null) {\n            this._thinInstanceDataStorage.previousMatrixBuffer = this._thinInstanceCreateMatrixBuffer(\"previousWorld\", buffer, staticBuffer);\n        }\n    }\n    else {\n        // color for instanced mesh is ColorInstanceKind and not ColorKind because of native that needs to do the differenciation\n        // hot switching kind here to preserve backward compatibility\n        if (kind === VertexBuffer.ColorKind) {\n            kind = VertexBuffer.ColorInstanceKind;\n        }\n        if (buffer === null) {\n            if (this._userThinInstanceBuffersStorage?.data[kind]) {\n                this.removeVerticesData(kind);\n                delete this._userThinInstanceBuffersStorage.data[kind];\n                delete this._userThinInstanceBuffersStorage.strides[kind];\n                delete this._userThinInstanceBuffersStorage.sizes[kind];\n                delete this._userThinInstanceBuffersStorage.vertexBuffers[kind];\n            }\n        }\n        else {\n            this._thinInstanceInitializeUserStorage();\n            this._userThinInstanceBuffersStorage.data[kind] = buffer;\n            this._userThinInstanceBuffersStorage.strides[kind] = stride;\n            this._userThinInstanceBuffersStorage.sizes[kind] = buffer.length;\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), buffer, kind, !staticBuffer, false, stride, true);\n            this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[kind]);\n        }\n    }\n};\nMesh.prototype.thinInstanceBufferUpdated = function (kind) {\n    if (kind === \"matrix\") {\n        if (this.thinInstanceAllowAutomaticStaticBufferRecreation && this._thinInstanceDataStorage.matrixBuffer && !this._thinInstanceDataStorage.matrixBuffer.isUpdatable()) {\n            this._thinInstanceRecreateBuffer(kind);\n        }\n        this._thinInstanceDataStorage.matrixBuffer?.updateDirectly(this._thinInstanceDataStorage.matrixData, 0, this._thinInstanceDataStorage.instancesCount);\n    }\n    else if (kind === \"previousMatrix\") {\n        if (this.thinInstanceAllowAutomaticStaticBufferRecreation &&\n            this._thinInstanceDataStorage.previousMatrixBuffer &&\n            !this._thinInstanceDataStorage.previousMatrixBuffer.isUpdatable()) {\n            this._thinInstanceRecreateBuffer(kind);\n        }\n        this._thinInstanceDataStorage.previousMatrixBuffer?.updateDirectly(this._thinInstanceDataStorage.previousMatrixData, 0, this._thinInstanceDataStorage.instancesCount);\n    }\n    else {\n        // preserve backward compatibility\n        if (kind === VertexBuffer.ColorKind) {\n            kind = VertexBuffer.ColorInstanceKind;\n        }\n        if (this._userThinInstanceBuffersStorage?.vertexBuffers[kind]) {\n            if (this.thinInstanceAllowAutomaticStaticBufferRecreation && !this._userThinInstanceBuffersStorage.vertexBuffers[kind].isUpdatable()) {\n                this._thinInstanceRecreateBuffer(kind);\n            }\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind].updateDirectly(this._userThinInstanceBuffersStorage.data[kind], 0);\n        }\n    }\n};\nMesh.prototype.thinInstancePartialBufferUpdate = function (kind, data, offset) {\n    if (kind === \"matrix\") {\n        if (this._thinInstanceDataStorage.matrixBuffer) {\n            this._thinInstanceDataStorage.matrixBuffer.updateDirectly(data, offset);\n        }\n    }\n    else {\n        // preserve backward compatibility\n        if (kind === VertexBuffer.ColorKind) {\n            kind = VertexBuffer.ColorInstanceKind;\n        }\n        if (this._userThinInstanceBuffersStorage?.vertexBuffers[kind]) {\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind].updateDirectly(data, offset);\n        }\n    }\n};\nMesh.prototype.thinInstanceGetWorldMatrices = function () {\n    if (!this._thinInstanceDataStorage.matrixData || !this._thinInstanceDataStorage.matrixBuffer) {\n        return [];\n    }\n    const matrixData = this._thinInstanceDataStorage.matrixData;\n    if (!this._thinInstanceDataStorage.worldMatrices) {\n        this._thinInstanceDataStorage.worldMatrices = [];\n        for (let i = 0; i < this._thinInstanceDataStorage.instancesCount; ++i) {\n            this._thinInstanceDataStorage.worldMatrices[i] = Matrix.FromArray(matrixData, i * 16);\n        }\n    }\n    return this._thinInstanceDataStorage.worldMatrices;\n};\nMesh.prototype.thinInstanceRefreshBoundingInfo = function (forceRefreshParentInfo = false, applySkeleton = false, applyMorph = false) {\n    if (!this._thinInstanceDataStorage.matrixData || !this._thinInstanceDataStorage.matrixBuffer) {\n        return;\n    }\n    const vectors = this._thinInstanceDataStorage.boundingVectors;\n    if (forceRefreshParentInfo || !this.rawBoundingInfo) {\n        vectors.length = 0;\n        this.refreshBoundingInfo(applySkeleton, applyMorph);\n        const boundingInfo = this.getBoundingInfo();\n        this.rawBoundingInfo = new BoundingInfo(boundingInfo.minimum, boundingInfo.maximum);\n    }\n    const boundingInfo = this.getBoundingInfo();\n    const matrixData = this._thinInstanceDataStorage.matrixData;\n    if (vectors.length === 0) {\n        for (let v = 0; v < boundingInfo.boundingBox.vectors.length; ++v) {\n            vectors.push(boundingInfo.boundingBox.vectors[v].clone());\n        }\n    }\n    TmpVectors.Vector3[0].setAll(Number.POSITIVE_INFINITY); // min\n    TmpVectors.Vector3[1].setAll(Number.NEGATIVE_INFINITY); // max\n    for (let i = 0; i < this._thinInstanceDataStorage.instancesCount; ++i) {\n        Matrix.FromArrayToRef(matrixData, i * 16, TmpVectors.Matrix[0]);\n        for (let v = 0; v < vectors.length; ++v) {\n            Vector3.TransformCoordinatesToRef(vectors[v], TmpVectors.Matrix[0], TmpVectors.Vector3[2]);\n            TmpVectors.Vector3[0].minimizeInPlace(TmpVectors.Vector3[2]);\n            TmpVectors.Vector3[1].maximizeInPlace(TmpVectors.Vector3[2]);\n        }\n    }\n    boundingInfo.reConstruct(TmpVectors.Vector3[0], TmpVectors.Vector3[1]);\n    this._updateBoundingInfo();\n};\nMesh.prototype._thinInstanceRecreateBuffer = function (kind, staticBuffer = true) {\n    if (kind === \"matrix\") {\n        this._thinInstanceDataStorage.matrixBuffer?.dispose();\n        this._thinInstanceDataStorage.matrixBuffer = this._thinInstanceCreateMatrixBuffer(\"world\", this._thinInstanceDataStorage.matrixData, staticBuffer);\n    }\n    else if (kind === \"previousMatrix\") {\n        if (this._scene.needsPreviousWorldMatrices) {\n            this._thinInstanceDataStorage.previousMatrixBuffer?.dispose();\n            this._thinInstanceDataStorage.previousMatrixBuffer = this._thinInstanceCreateMatrixBuffer(\"previousWorld\", this._thinInstanceDataStorage.previousMatrixData ?? this._thinInstanceDataStorage.matrixData, staticBuffer);\n        }\n    }\n    else {\n        if (kind === VertexBuffer.ColorKind) {\n            kind = VertexBuffer.ColorInstanceKind;\n        }\n        this._userThinInstanceBuffersStorage.vertexBuffers[kind]?.dispose();\n        this._userThinInstanceBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), this._userThinInstanceBuffersStorage.data[kind], kind, !staticBuffer, false, this._userThinInstanceBuffersStorage.strides[kind], true);\n        this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[kind]);\n    }\n};\nMesh.prototype._thinInstanceUpdateBufferSize = function (kind, numInstances = 1) {\n    // preserve backward compatibility\n    if (kind === VertexBuffer.ColorKind) {\n        kind = VertexBuffer.ColorInstanceKind;\n    }\n    const kindIsMatrix = kind === \"matrix\";\n    if (!kindIsMatrix && (!this._userThinInstanceBuffersStorage || !this._userThinInstanceBuffersStorage.strides[kind])) {\n        return;\n    }\n    const stride = kindIsMatrix ? 16 : this._userThinInstanceBuffersStorage.strides[kind];\n    const currentSize = kindIsMatrix ? this._thinInstanceDataStorage.matrixBufferSize : this._userThinInstanceBuffersStorage.sizes[kind];\n    let data = kindIsMatrix ? this._thinInstanceDataStorage.matrixData : this._userThinInstanceBuffersStorage.data[kind];\n    const bufferSize = (this._thinInstanceDataStorage.instancesCount + numInstances) * stride;\n    let newSize = currentSize;\n    while (newSize < bufferSize) {\n        newSize *= 2;\n    }\n    if (!data || currentSize != newSize) {\n        if (!data) {\n            data = new Float32Array(newSize);\n        }\n        else {\n            const newData = new Float32Array(newSize);\n            newData.set(data, 0);\n            data = newData;\n        }\n        if (kindIsMatrix) {\n            this._thinInstanceDataStorage.matrixBuffer?.dispose();\n            this._thinInstanceDataStorage.matrixBuffer = this._thinInstanceCreateMatrixBuffer(\"world\", data, false);\n            this._thinInstanceDataStorage.matrixData = data;\n            this._thinInstanceDataStorage.matrixBufferSize = newSize;\n            if (this._scene.needsPreviousWorldMatrices && !this._thinInstanceDataStorage.previousMatrixData) {\n                this._thinInstanceDataStorage.previousMatrixBuffer?.dispose();\n                this._thinInstanceDataStorage.previousMatrixBuffer = this._thinInstanceCreateMatrixBuffer(\"previousWorld\", data, false);\n            }\n        }\n        else {\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind]?.dispose();\n            this._userThinInstanceBuffersStorage.data[kind] = data;\n            this._userThinInstanceBuffersStorage.sizes[kind] = newSize;\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), data, kind, true, false, stride, true);\n            this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[kind]);\n        }\n    }\n};\nMesh.prototype._thinInstanceInitializeUserStorage = function () {\n    if (!this._userThinInstanceBuffersStorage) {\n        this._userThinInstanceBuffersStorage = {\n            data: {},\n            sizes: {},\n            vertexBuffers: {},\n            strides: {},\n        };\n    }\n};\nMesh.prototype._disposeThinInstanceSpecificData = function () {\n    if (this._thinInstanceDataStorage?.matrixBuffer) {\n        this._thinInstanceDataStorage.matrixBuffer.dispose();\n        this._thinInstanceDataStorage.matrixBuffer = null;\n    }\n};\n//# sourceMappingURL=thinInstanceMesh.js.map"], "names": ["<PERSON><PERSON>", "matrix", "refresh", "<PERSON><PERSON>", "index", "i", "Matrix", "kind", "stride", "VertexBuffer", "matrixData", "value", "numMaxInstances", "buffer", "staticBuffer", "matrixBuffer", "<PERSON><PERSON><PERSON>", "data", "offset", "forceRefreshParentInfo", "applySkeleton", "applyMorph", "vectors", "boundingInfo", "BoundingInfo", "v", "TmpVectors", "Vector3", "numInstances", "kindIsMatrix", "currentSize", "bufferSize", "newSize", "newData"], "mappings": "6FAKAA,EAAK,UAAU,gBAAkB,SAAUC,EAAQC,EAAU,GAAM,CAC/D,GAAI,CAAC,KAAK,SAAU,EAAC,UAAS,EAAG,QAAS,EAAC,gBACvC,OAAAC,EAAO,MAAM,4FAA4F,EAClG,GAEX,KAAK,8BAA8B,SAAU,MAAM,QAAQF,CAAM,EAAIA,EAAO,OAAS,CAAC,EACtF,MAAMG,EAAQ,KAAK,yBAAyB,eAC5C,GAAI,MAAM,QAAQH,CAAM,EACpB,QAASI,EAAI,EAAGA,EAAIJ,EAAO,OAAQ,EAAEI,EACjC,KAAK,wBAAwB,KAAK,yBAAyB,iBAAkBJ,EAAOI,CAAC,EAAGA,IAAMJ,EAAO,OAAS,GAAKC,CAAO,OAI9H,KAAK,wBAAwB,KAAK,yBAAyB,iBAAkBD,EAAQC,CAAO,EAEhG,OAAOE,CACX,EACAJ,EAAK,UAAU,oBAAsB,SAAUE,EAAU,GAAM,CAC3D,OAAO,KAAK,gBAAgBI,EAAO,iBAAkBJ,CAAO,CAChE,EACAF,EAAK,UAAU,8BAAgC,SAAUO,EAAMC,EAAQ,CAE/DD,IAASE,EAAa,YACtBF,EAAOE,EAAa,mBAExB,KAAK,mBAAmBF,CAAI,EAC5B,KAAK,mCAAkC,EACvC,KAAK,gCAAgC,QAAQA,CAAI,EAAIC,EACrD,KAAK,gCAAgC,MAAMD,CAAI,EAAIC,EAAS,KAAK,IAAI,GAAI,KAAK,yBAAyB,cAAc,EACrH,KAAK,gCAAgC,KAAKD,CAAI,EAAI,IAAI,aAAa,KAAK,gCAAgC,MAAMA,CAAI,CAAC,EACnH,KAAK,gCAAgC,cAAcA,CAAI,EAAI,IAAIE,EAAa,KAAK,YAAa,KAAK,gCAAgC,KAAKF,CAAI,EAAGA,EAAM,GAAM,GAAOC,EAAQ,EAAI,EAC9K,KAAK,kBAAkB,KAAK,gCAAgC,cAAcD,CAAI,CAAC,CACnF,EACAP,EAAK,UAAU,wBAA0B,SAAUI,EAAOH,EAAQC,EAAU,GAAM,CAC9E,GAAI,CAAC,KAAK,yBAAyB,YAAcE,GAAS,KAAK,yBAAyB,eACpF,MAAO,GAEX,MAAMM,EAAa,KAAK,yBAAyB,WACjD,OAAAT,EAAO,YAAYS,EAAYN,EAAQ,EAAE,EACrC,KAAK,yBAAyB,gBAC9B,KAAK,yBAAyB,cAAcA,CAAK,EAAIH,GAErDC,IACA,KAAK,0BAA0B,QAAQ,EAClC,KAAK,uBACN,KAAK,gCAAgC,EAAK,GAG3C,EACX,EACAF,EAAK,UAAU,2BAA6B,SAAUO,EAAMH,EAAOO,EAAOT,EAAU,GAAM,CAKtF,OAHIK,IAASE,EAAa,YACtBF,EAAOE,EAAa,mBAEpB,CAAC,KAAK,iCAAmC,CAAC,KAAK,gCAAgC,KAAKF,CAAI,GAAKH,GAAS,KAAK,yBAAyB,eAC7H,IAEX,KAAK,8BAA8BG,EAAM,CAAC,EAC1C,KAAK,gCAAgC,KAAKA,CAAI,EAAE,IAAII,EAAOP,EAAQ,KAAK,gCAAgC,QAAQG,CAAI,CAAC,EACjHL,GACA,KAAK,0BAA0BK,CAAI,EAEhC,GACX,EACA,OAAO,eAAeP,EAAK,UAAW,oBAAqB,CACvD,IAAK,UAAY,CACb,OAAO,KAAK,yBAAyB,cACxC,EACD,IAAK,SAAUW,EAAO,CAClB,MAAMD,EAAa,KAAK,yBAAyB,YAAc,KAAK,QAAQ,yBAAyB,WAC/FE,EAAkBF,EAAaA,EAAW,OAAS,GAAK,EAC1DC,GAASC,IACT,KAAK,yBAAyB,eAAiBD,EAEtD,EACD,WAAY,GACZ,aAAc,EAClB,CAAC,EACDX,EAAK,UAAU,gCAAkC,SAAUO,EAAMM,EAAQC,EAAe,GAAM,CAC1F,MAAMC,EAAe,IAAIC,EAAO,KAAK,UAAW,EAAEH,EAAQ,CAACC,EAAc,GAAI,GAAO,EAAI,EACxF,QAAST,EAAI,EAAGA,EAAI,EAAGA,IACnB,KAAK,kBAAkBU,EAAa,mBAAmBR,EAAOF,EAAGA,EAAI,EAAG,CAAC,CAAC,EAE9E,OAAOU,CACX,EACAf,EAAK,UAAU,sBAAwB,SAAUO,EAAMM,EAAQL,EAAS,EAAGM,EAAe,GAAM,CAC5FN,EAASA,GAAU,GACfD,IAAS,UACT,KAAK,yBAAyB,cAAc,UAC5C,KAAK,yBAAyB,aAAe,KAC7C,KAAK,yBAAyB,iBAAmBM,EAASA,EAAO,OAAS,GAAKL,EAC/E,KAAK,yBAAyB,WAAaK,EAC3C,KAAK,yBAAyB,cAAgB,KAC1CA,IAAW,MACX,KAAK,yBAAyB,eAAiBA,EAAO,OAASL,EAC/D,KAAK,yBAAyB,aAAe,KAAK,gCAAgC,QAASK,EAAQC,CAAY,EAC1G,KAAK,uBACN,KAAK,gCAAgC,EAAK,IAI9C,KAAK,yBAAyB,eAAiB,EAC1C,KAAK,uBAEN,KAAK,oBAAmB,IAI3BP,IAAS,kBACd,KAAK,yBAAyB,sBAAsB,UACpD,KAAK,yBAAyB,qBAAuB,KACrD,KAAK,yBAAyB,mBAAqBM,EAC/CA,IAAW,OACX,KAAK,yBAAyB,qBAAuB,KAAK,gCAAgC,gBAAiBA,EAAQC,CAAY,KAM/HP,IAASE,EAAa,YACtBF,EAAOE,EAAa,mBAEpBI,IAAW,KACP,KAAK,iCAAiC,KAAKN,CAAI,IAC/C,KAAK,mBAAmBA,CAAI,EAC5B,OAAO,KAAK,gCAAgC,KAAKA,CAAI,EACrD,OAAO,KAAK,gCAAgC,QAAQA,CAAI,EACxD,OAAO,KAAK,gCAAgC,MAAMA,CAAI,EACtD,OAAO,KAAK,gCAAgC,cAAcA,CAAI,IAIlE,KAAK,mCAAkC,EACvC,KAAK,gCAAgC,KAAKA,CAAI,EAAIM,EAClD,KAAK,gCAAgC,QAAQN,CAAI,EAAIC,EACrD,KAAK,gCAAgC,MAAMD,CAAI,EAAIM,EAAO,OAC1D,KAAK,gCAAgC,cAAcN,CAAI,EAAI,IAAIE,EAAa,KAAK,UAAW,EAAEI,EAAQN,EAAM,CAACO,EAAc,GAAON,EAAQ,EAAI,EAC9I,KAAK,kBAAkB,KAAK,gCAAgC,cAAcD,CAAI,CAAC,GAG3F,EACAP,EAAK,UAAU,0BAA4B,SAAUO,EAAM,CACnDA,IAAS,UACL,KAAK,kDAAoD,KAAK,yBAAyB,cAAgB,CAAC,KAAK,yBAAyB,aAAa,eACnJ,KAAK,4BAA4BA,CAAI,EAEzC,KAAK,yBAAyB,cAAc,eAAe,KAAK,yBAAyB,WAAY,EAAG,KAAK,yBAAyB,cAAc,GAE/IA,IAAS,kBACV,KAAK,kDACL,KAAK,yBAAyB,sBAC9B,CAAC,KAAK,yBAAyB,qBAAqB,YAAW,GAC/D,KAAK,4BAA4BA,CAAI,EAEzC,KAAK,yBAAyB,sBAAsB,eAAe,KAAK,yBAAyB,mBAAoB,EAAG,KAAK,yBAAyB,cAAc,IAIhKA,IAASE,EAAa,YACtBF,EAAOE,EAAa,mBAEpB,KAAK,iCAAiC,cAAcF,CAAI,IACpD,KAAK,kDAAoD,CAAC,KAAK,gCAAgC,cAAcA,CAAI,EAAE,eACnH,KAAK,4BAA4BA,CAAI,EAEzC,KAAK,gCAAgC,cAAcA,CAAI,EAAE,eAAe,KAAK,gCAAgC,KAAKA,CAAI,EAAG,CAAC,GAGtI,EACAP,EAAK,UAAU,gCAAkC,SAAUO,EAAMU,EAAMC,EAAQ,CACvEX,IAAS,SACL,KAAK,yBAAyB,cAC9B,KAAK,yBAAyB,aAAa,eAAeU,EAAMC,CAAM,GAKtEX,IAASE,EAAa,YACtBF,EAAOE,EAAa,mBAEpB,KAAK,iCAAiC,cAAcF,CAAI,GACxD,KAAK,gCAAgC,cAAcA,CAAI,EAAE,eAAeU,EAAMC,CAAM,EAGhG,EACAlB,EAAK,UAAU,6BAA+B,UAAY,CACtD,GAAI,CAAC,KAAK,yBAAyB,YAAc,CAAC,KAAK,yBAAyB,aAC5E,MAAO,GAEX,MAAMU,EAAa,KAAK,yBAAyB,WACjD,GAAI,CAAC,KAAK,yBAAyB,cAAe,CAC9C,KAAK,yBAAyB,cAAgB,GAC9C,QAASL,EAAI,EAAGA,EAAI,KAAK,yBAAyB,eAAgB,EAAEA,EAChE,KAAK,yBAAyB,cAAcA,CAAC,EAAIC,EAAO,UAAUI,EAAYL,EAAI,EAAE,CAE3F,CACD,OAAO,KAAK,yBAAyB,aACzC,EACAL,EAAK,UAAU,gCAAkC,SAAUmB,EAAyB,GAAOC,EAAgB,GAAOC,EAAa,GAAO,CAClI,GAAI,CAAC,KAAK,yBAAyB,YAAc,CAAC,KAAK,yBAAyB,aAC5E,OAEJ,MAAMC,EAAU,KAAK,yBAAyB,gBAC9C,GAAIH,GAA0B,CAAC,KAAK,gBAAiB,CACjDG,EAAQ,OAAS,EACjB,KAAK,oBAAoBF,EAAeC,CAAU,EAClD,MAAME,EAAe,KAAK,kBAC1B,KAAK,gBAAkB,IAAIC,EAAaD,EAAa,QAASA,EAAa,OAAO,CACrF,CACD,MAAMA,EAAe,KAAK,kBACpBb,EAAa,KAAK,yBAAyB,WACjD,GAAIY,EAAQ,SAAW,EACnB,QAASG,EAAI,EAAGA,EAAIF,EAAa,YAAY,QAAQ,OAAQ,EAAEE,EAC3DH,EAAQ,KAAKC,EAAa,YAAY,QAAQE,CAAC,EAAE,MAAK,CAAE,EAGhEC,EAAW,QAAQ,CAAC,EAAE,OAAO,OAAO,iBAAiB,EACrDA,EAAW,QAAQ,CAAC,EAAE,OAAO,OAAO,iBAAiB,EACrD,QAASrB,EAAI,EAAGA,EAAI,KAAK,yBAAyB,eAAgB,EAAEA,EAAG,CACnEC,EAAO,eAAeI,EAAYL,EAAI,GAAIqB,EAAW,OAAO,CAAC,CAAC,EAC9D,QAASD,EAAI,EAAGA,EAAIH,EAAQ,OAAQ,EAAEG,EAClCE,EAAQ,0BAA0BL,EAAQG,CAAC,EAAGC,EAAW,OAAO,CAAC,EAAGA,EAAW,QAAQ,CAAC,CAAC,EACzFA,EAAW,QAAQ,CAAC,EAAE,gBAAgBA,EAAW,QAAQ,CAAC,CAAC,EAC3DA,EAAW,QAAQ,CAAC,EAAE,gBAAgBA,EAAW,QAAQ,CAAC,CAAC,CAElE,CACDH,EAAa,YAAYG,EAAW,QAAQ,CAAC,EAAGA,EAAW,QAAQ,CAAC,CAAC,EACrE,KAAK,oBAAmB,CAC5B,EACA1B,EAAK,UAAU,4BAA8B,SAAUO,EAAMO,EAAe,GAAM,CAC1EP,IAAS,UACT,KAAK,yBAAyB,cAAc,UAC5C,KAAK,yBAAyB,aAAe,KAAK,gCAAgC,QAAS,KAAK,yBAAyB,WAAYO,CAAY,GAE5IP,IAAS,iBACV,KAAK,OAAO,6BACZ,KAAK,yBAAyB,sBAAsB,UACpD,KAAK,yBAAyB,qBAAuB,KAAK,gCAAgC,gBAAiB,KAAK,yBAAyB,oBAAsB,KAAK,yBAAyB,WAAYO,CAAY,IAIrNP,IAASE,EAAa,YACtBF,EAAOE,EAAa,mBAExB,KAAK,gCAAgC,cAAcF,CAAI,GAAG,QAAO,EACjE,KAAK,gCAAgC,cAAcA,CAAI,EAAI,IAAIE,EAAa,KAAK,UAAS,EAAI,KAAK,gCAAgC,KAAKF,CAAI,EAAGA,EAAM,CAACO,EAAc,GAAO,KAAK,gCAAgC,QAAQP,CAAI,EAAG,EAAI,EACnO,KAAK,kBAAkB,KAAK,gCAAgC,cAAcA,CAAI,CAAC,EAEvF,EACAP,EAAK,UAAU,8BAAgC,SAAUO,EAAMqB,EAAe,EAAG,CAEzErB,IAASE,EAAa,YACtBF,EAAOE,EAAa,mBAExB,MAAMoB,EAAetB,IAAS,SAC9B,GAAI,CAACsB,IAAiB,CAAC,KAAK,iCAAmC,CAAC,KAAK,gCAAgC,QAAQtB,CAAI,GAC7G,OAEJ,MAAMC,EAASqB,EAAe,GAAK,KAAK,gCAAgC,QAAQtB,CAAI,EAC9EuB,EAAcD,EAAe,KAAK,yBAAyB,iBAAmB,KAAK,gCAAgC,MAAMtB,CAAI,EACnI,IAAIU,EAAOY,EAAe,KAAK,yBAAyB,WAAa,KAAK,gCAAgC,KAAKtB,CAAI,EACnH,MAAMwB,GAAc,KAAK,yBAAyB,eAAiBH,GAAgBpB,EACnF,IAAIwB,EAAUF,EACd,KAAOE,EAAUD,GACbC,GAAW,EAEf,GAAI,CAACf,GAAQa,GAAeE,EAAS,CACjC,GAAI,CAACf,EACDA,EAAO,IAAI,aAAae,CAAO,MAE9B,CACD,MAAMC,EAAU,IAAI,aAAaD,CAAO,EACxCC,EAAQ,IAAIhB,EAAM,CAAC,EACnBA,EAAOgB,CACV,CACGJ,GACA,KAAK,yBAAyB,cAAc,UAC5C,KAAK,yBAAyB,aAAe,KAAK,gCAAgC,QAASZ,EAAM,EAAK,EACtG,KAAK,yBAAyB,WAAaA,EAC3C,KAAK,yBAAyB,iBAAmBe,EAC7C,KAAK,OAAO,4BAA8B,CAAC,KAAK,yBAAyB,qBACzE,KAAK,yBAAyB,sBAAsB,UACpD,KAAK,yBAAyB,qBAAuB,KAAK,gCAAgC,gBAAiBf,EAAM,EAAK,KAI1H,KAAK,gCAAgC,cAAcV,CAAI,GAAG,QAAO,EACjE,KAAK,gCAAgC,KAAKA,CAAI,EAAIU,EAClD,KAAK,gCAAgC,MAAMV,CAAI,EAAIyB,EACnD,KAAK,gCAAgC,cAAczB,CAAI,EAAI,IAAIE,EAAa,KAAK,UAAS,EAAIQ,EAAMV,EAAM,GAAM,GAAOC,EAAQ,EAAI,EACnI,KAAK,kBAAkB,KAAK,gCAAgC,cAAcD,CAAI,CAAC,EAEtF,CACL,EACAP,EAAK,UAAU,mCAAqC,UAAY,CACvD,KAAK,kCACN,KAAK,gCAAkC,CACnC,KAAM,CAAE,EACR,MAAO,CAAE,EACT,cAAe,CAAE,EACjB,QAAS,CAAE,CACvB,EAEA,EACAA,EAAK,UAAU,iCAAmC,UAAY,CACtD,KAAK,0BAA0B,eAC/B,KAAK,yBAAyB,aAAa,UAC3C,KAAK,yBAAyB,aAAe,KAErD", "x_google_ignoreList": [0]}