/* AI Therapist Frontend Styles */

:root {
    --color-accent: #6366f1;
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-text: #e2e8f0;
    --color-text-secondary: #94a3b8;
    --color-border: rgba(255, 255, 255, 0.1);
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --boxSize: 8px;
    --gutter: 4px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, var(--color-background) 0%, #1e1b4b 100%);
    color: var(--color-text);
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    width: 100%;
    margin-bottom: 2rem;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.header-left h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.subtitle {
    text-align: center;
    max-width: 1200px;
    margin: 1rem auto 0;
    padding: 0 2rem;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
}

.disclaimer {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem auto 0;
    max-width: 1200px;
    font-size: 0.9rem;
    line-height: 1.5;
    color: #fbbf24;
}



.container {
    width: 90%;
    max-width: 1200px;
    background: rgba(30, 41, 59, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: 0 auto;
    flex: 1;
}

.mode-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 0.75rem;
    padding: 0.5rem;
}

.mode-btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--color-text);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.mode-btn.active {
    background: var(--color-accent);
    color: white;
}

.mode-btn:hover:not(.active) {
    background: rgba(99, 102, 241, 0.2);
}

/* Voice Mode Styles */
.voice-section {
    display: none;
}

.voice-section.active {
    display: block;
}

.controls {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #cbd5e1;
}

input, select {
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(15, 23, 42, 0.8);
    color: var(--color-text);
    font-size: 1rem;
    transition: all 0.3s ease;
}

input:focus, select:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.wave-container {
    position: relative;
    display: flex;
    min-height: 120px;
    max-height: 140px;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
    background: rgba(15, 23, 42, 0.3);
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.box-container {
    display: flex;
    justify-content: space-between;
    height: 64px;
    width: 100%;
    max-width: 400px;
    padding: 0 1rem;
}

.box {
    height: 100%;
    width: var(--boxSize);
    background: linear-gradient(135deg, var(--color-accent), #8b5cf6);
    border-radius: 8px;
    transition: transform 0.05s ease;
    margin: 0 1px;
}

/* Video Mode Styles */
.video-section {
    display: none;
}

.video-section.active {
    display: block;
}

.video-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 2rem 0;
    height: 400px;
}

.video-panel {
    background: rgba(15, 23, 42, 0.8);
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.video-element {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 1rem;
}

.video-label {
    position: absolute;
    top: 10px;
    left: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    z-index: 10;
}

.video-status {
    position: absolute;
    bottom: 10px;
    right: 15px;
    background: rgba(16, 185, 129, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    z-index: 10;
}

.file-upload-panel {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2rem;
}

.upload-zone {
    border: 2px dashed rgba(99, 102, 241, 0.3);
    border-radius: 1rem;
    padding: 2rem;
    background: rgba(99, 102, 241, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-zone:hover {
    border-color: var(--color-accent);
    background: rgba(99, 102, 241, 0.1);
}

.upload-zone.dragover {
    border-color: var(--color-accent);
    background: rgba(99, 102, 241, 0.15);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.upload-text {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.upload-subtext {
    font-size: 0.9rem;
    opacity: 0.7;
}

.file-input {
    display: none;
}

.uploaded-files {
    margin-top: 1rem;
    max-height: 150px;
    overflow-y: auto;
}

.uploaded-file {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    margin: 0.5rem 0;
    display: flex;
    justify-content: between;
    align-items: center;
    font-size: 0.9rem;
}

.file-remove {
    background: none;
    border: none;
    color: var(--color-error);
    cursor: pointer;
    margin-left: auto;
    padding: 0.25rem;
}

.video-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.video-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    border: none;
    background: linear-gradient(135deg, var(--color-accent), #8b5cf6);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 150px;
}

.video-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.video-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.video-btn.danger {
    background: linear-gradient(135deg, var(--color-error), #dc2626);
}

.voice-button {
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    border: none;
    background: linear-gradient(135deg, var(--color-accent), #8b5cf6);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    min-width: 200px;
    margin: 0 auto;
    font-size: 1.1rem;
}

.voice-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.voice-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.voice-button.danger {
    background: linear-gradient(135deg, var(--color-error), #dc2626);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid white;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    flex-shrink: 0;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.pulse-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.pulse-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    opacity: 0.7;
    flex-shrink: 0;
    transform: scale(var(--audio-level, 1));
    transition: transform 0.1s ease;
}

.mute-toggle {
    width: 28px;
    height: 28px;
    cursor: pointer;
    flex-shrink: 0;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.mute-toggle:hover {
    opacity: 1;
}

.mute-toggle svg {
    display: block;
    width: 100%;
    height: 100%;
}

/* Text Mode Styles */
.text-section {
    display: none;
}

.text-section.active {
    display: block;
}

.chat-container {
    height: 50vh;
    border-radius: 0.75rem;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(15, 23, 42, 0.5);
}

.messages {
    height: calc(100% - 80px);
    overflow-y: auto;
    padding: 1rem;
}

.message {
    display: flex;
    margin-bottom: 1rem;
    animation: fadeIn 0.3s ease-in-out;
}

.user-message {
    flex-direction: row-reverse;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
    background: linear-gradient(135deg, var(--color-accent), #8b5cf6);
    color: white;
    font-size: 1.2rem;
}

.user-message .avatar {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.bubble {
    max-width: 70%;
    padding: 12px 15px;
    border-radius: 18px;
    position: relative;
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-message .bubble {
    border-top-left-radius: 5px;
}

.user-message .bubble {
    border-top-right-radius: 5px;
    background: rgba(6, 182, 212, 0.2);
    border-color: rgba(6, 182, 212, 0.3);
}

.bubble p {
    margin: 0;
    line-height: 1.5;
}

.input-area {
    display: flex;
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(15, 23, 42, 0.8);
}

.text-input {
    flex: 1;
    border: none;
    padding: 10px 15px;
    border-radius: 20px;
    resize: none;
    background: rgba(30, 41, 59, 0.8);
    color: var(--color-text);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    font-family: inherit;
    font-size: 1rem;
}

.text-input:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-buttons {
    display: flex;
    gap: 0.5rem;
    margin-left: 0.75rem;
}

.input-btn {
    background: none;
    border: none;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: var(--color-accent);
}

.input-btn:hover {
    background-color: rgba(99, 102, 241, 0.1);
}

.input-btn:active {
    background: var(--color-accent);
    color: white;
    animation: pulse 1.5s infinite;
}



@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.call-status.ended {
    background: rgba(99, 102, 241, 0.2);
    color: var(--color-accent);
    border: 1px solid rgba(99, 102, 241, 0.3);
}

.call-status.missed {
    background: rgba(239, 68, 68, 0.2);
    color: var(--color-error);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.no-calls {
    text-align: center;
    color: #94a3b8;
    padding: 2rem;
    font-style: italic;
}

/* App Logo Styles */
.app-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.app-logo-img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
}

.app-logo h1 {
    margin: 0;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
}

/* Footer Styles */
        .footer {
            width: 100%;
            background: rgba(15, 23, 42, 0.95);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            text-align: center;
            margin-top: 3rem;
        }

        .footer-content {
            font-size: 0.9rem;
            color: #94a3b8;
        }
       .footer-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
      }

      .footer-logo-img {
       width: 32px;
       height: 32px;
       border-radius: 6px;
       object-fit: cover;
      }

   .footer-brand {
     font-size: 1.25rem;
     font-weight: 700;
     background: linear-gradient(135deg, #6366f1, #8b5cf6);
     -webkit-background-clip: text;
     background-clip: text;
     color: transparent;
    }

    .footer-description {
      font-size: 0.9rem;
      color: #94a3b8;
      margin: 0.5rem 0 1.5rem;
       }
      .footer-links {
       display: flex;
       justify-content: center;
       gap: 2rem;
       margin-bottom: 1.5rem;
       }
       .footer-copyright {
        font-size: 0.85rem;
        color: #64748b;
        margin: 0;
        padding-top: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
         }
        .footer-link {
            color: var(--color-accent);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .footer-link:hover {
            text-decoration: underline;
            transform: translateY(-1px);
        }

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 16px 24px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 1000;
    display: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.toast.error {
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.toast.warning {
    background: rgba(245, 158, 11, 0.9);
    color: white;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.toast.success {
    background: rgba(16, 185, 129, 0.9);
    color: white;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Status indicators */
.status-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(30, 41, 59, 0.9);
    color: var(--color-text);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.status-indicator.visible {
    opacity: 1;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

@keyframes flash {
    0% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(34, 197, 94, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

/* Authentication Styles */
.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.header-left h1 {
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--color-text);
}

.user-menu-btn, .ManageSubscription-btn {
    background: var(--color-accent);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.user-menu-btn:hover, .logout-btn:hover {
    background: var(--color-accent-dark);
}

.subscription-info {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.subscription-card h3 {
    margin: 0 0 1rem 0;
    color: var(--color-accent);
}

.usage-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.usage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.upgrade-btn, .billing-btn {
    background: var(--color-accent);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    width: 100%;
}

.upgrade-btn:hover, .billing-btn:hover {
    background: var(--color-accent-dark);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: var(--color-surface);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.auth-modal {
    max-width: 400px;
}

.auth-modal h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--color-accent);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--color-text);
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    background: var(--color-background);
    color: var(--color-text);
    font-size: 1rem;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.error-message {
    color: var(--color-error);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: none;
}

.auth-btn {
    width: 100%;
    background: var(--color-accent);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 1rem;
}

.auth-btn:hover {
    background: var(--color-accent-dark);
}

.auth-switch {
    text-align: center;
    margin: 0;
    color: var(--color-text-secondary);
}

.auth-switch a {
    color: var(--color-accent);
    text-decoration: none;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Upgrade Modal Styles */
.upgrade-modal {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.modal-header h2 {
    margin: 0;
    color: var(--color-accent);
}

.close {
    font-size: 2rem;
    cursor: pointer;
    color: var(--color-text-secondary);
}

.close:hover {
    color: var(--color-text);
}

.plans-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Upgrade Modal Pricing - Match Landing Page Design */
.upgrade-modal .pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.upgrade-modal .pricing-card {
    background: var(--color-surface);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(99, 102, 241, 0.2);
    transition: all 0.3s ease;
    position: relative;
    text-align: center;
}

.upgrade-modal .pricing-card.popular {
    border-color: var(--color-accent);
    transform: scale(1.05);
    background: linear-gradient(135deg, var(--color-surface) 0%, rgba(99, 102, 241, 0.1) 100%);
}

.upgrade-modal .pricing-card.popular::before {
    content: 'Most Popular';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.9rem;
    font-weight: 600;
}

.upgrade-modal .pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.upgrade-modal .pricing-card.popular:hover {
    transform: scale(1.05) translateY(-5px);
}

.upgrade-modal .plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text);
    margin: 0 0 0.5rem 0;
}

.upgrade-modal .plan-description {
    color: var(--color-text-secondary);
    margin: 0 0 1.5rem 0;
    font-size: 0.95rem;
}

.upgrade-modal .plan-price {
    margin-bottom: 2rem;
}

.upgrade-modal .plan-price .currency {
    font-size: 1.2rem;
    vertical-align: top;
    color: var(--color-accent);
    font-weight: 600;
}

.upgrade-modal .plan-price .amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--color-accent);
    line-height: 1;
}

.upgrade-modal .plan-price .period {
    font-size: 1rem;
    color: var(--color-text-secondary);
    font-weight: 500;
}

.upgrade-modal .plan-features {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
    text-align: left;
}

.upgrade-modal .plan-features li {
    padding: 0.75rem 0;
    color: var(--color-text);
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.upgrade-modal .plan-features li:last-child {
    border-bottom: none;
}

.upgrade-modal .feature-icon {
    color: var(--color-success);
    font-weight: bold;
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.upgrade-modal .plan-button {
    width: 100%;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
}

.upgrade-modal .plan-button.premium-button {
    background: linear-gradient(135deg, var(--color-accent) 0%, #8b5cf6 100%);
    color: white;
}

.upgrade-modal .plan-button.basic-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.upgrade-modal .plan-button.current-plan {
    background: var(--color-text);
    color: var(--color-background);
    cursor: not-allowed;
    opacity: 0.7;
}

.upgrade-modal .plan-button:hover:not(:disabled):not(.current-plan) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

.upgrade-modal .plan-button.premium-button:hover {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

.upgrade-modal .plan-button.basic-button:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.plan-card {
    background: var(--color-background);
    border: 2px solid var(--color-border);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.plan-card:hover {
    border-color: var(--color-accent);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(99, 102, 241, 0.2);
}

.plan-card h3 {
    margin: 0 0 1rem 0;
    color: var(--color-accent);
    font-size: 1.5rem;
}

.plan-price {
    font-size: 2rem;
    font-weight: bold;
    color: var(--color-text);
    margin-bottom: 1rem;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.plan-features li {
    padding: 0.5rem 0;
    color: var(--color-text-secondary);
    border-bottom: 1px solid var(--color-border);
}

.plan-features li:last-child {
    border-bottom: none;
}

.subscribe-btn {
    width: 100%;
    background: var(--color-accent);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
}

.subscribe-btn:hover {
    background: var(--color-accent-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        width: 95%;
        padding: 1.5rem;
    }

    .header h1 {
        font-size: 2rem;
    }

    .bubble {
        max-width: 85%;
    }

    .mode-selector {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chat-container {
        height: 60vh;
    }

    .voice-button, .video-btn {
        min-width: 140px;
        font-size: 0.9rem;
    }

    .video-container {
        grid-template-columns: 1fr;
        height: 300px;
    }

    .video-controls {
        flex-direction: column;
        align-items: center;
    }

    .footer {
        padding: 1.25rem;
    }

    .footer-content {
        font-size: 0.8rem;
    }

    .phone-number {
        font-size: 1.5rem;
    }

    .call-log-entry {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8rem;
    }

    .bubble {
        max-width: 90%;
    }

    .input-area {
        flex-direction: column;
        gap: 0.75rem;
    }

    .input-buttons {
        justify-content: center;
        margin-left: 0;
    }

    .footer-content {
        font-size: 0.75rem;
        line-height: 1.4;
    }

    .phone-number {
        font-size: 1.2rem;
    }

    .mode-selector {
        padding: 0.25rem;
    }

    .mode-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Subscription Management Section */
.subscription-section {
    background: var(--color-background);
    padding: 3rem 0;
    margin-top: 2rem;
    border-top: 1px solid var(--color-border);
}

.subscription-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.subscription-header {
    text-align: center;
    margin-bottom: 3rem;
}

.subscription-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--color-accent) 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subscription-header p {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    margin: 0;
}

.current-plan-info {
    margin-bottom: 3rem;
}

.current-plan-card {
    background: var(--color-surface);
    border-radius: 1rem;
    padding: 2rem;
    border: 2px solid var(--color-accent);
    text-align: center;
    max-width: 400px;
    margin: 0 auto;
}

.current-plan-card h3 {
    margin: 0 0 1rem 0;
    color: var(--color-accent);
    font-size: 1.3rem;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text);
    margin-bottom: 0.5rem;
}

.plan-usage {
    color: var(--color-text-secondary);
    font-size: 0.95rem;
}

.available-plans h3 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    color: var(--color-text);
}

.subscription-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.subscription-plan-card {
    background: var(--color-surface);
    border-radius: 1.5rem;
    padding: 2.5rem 2rem;
    border: 2px solid rgba(99, 102, 241, 0.2);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.subscription-plan-card:hover {
    transform: translateY(-8px);
    border-color: var(--color-accent);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);
}

.subscription-plan-card.current-plan {
    border-color: #10b981;
    background: linear-gradient(135deg, var(--color-surface) 0%, rgba(16, 185, 129, 0.1) 100%);
}

.subscription-plan-card.current-plan::before {
    content: 'Current Plan';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: #10b981;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.subscription-plan-card.popular {
    border-color: var(--color-accent);
    transform: scale(1.05);
    background: linear-gradient(135deg, var(--color-surface) 0%, rgba(99, 102, 241, 0.1) 100%);
    box-shadow: 0 15px 35px rgba(99, 102, 241, 0.25);
}

.subscription-plan-card.popular::before {
    content: 'Most Popular';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.subscription-plan-card h4 {
    font-size: 1.5rem;
    margin: 0 0 1rem 0;
    color: var(--color-text);
    font-weight: 700;
}

.subscription-plan-card .plan-price {
    margin: 1rem 0;
}

.subscription-plan-card .plan-price .currency {
    font-size: 1rem;
    color: var(--color-accent);
}

.subscription-plan-card .plan-price .amount {
    font-size: 3rem;
    font-weight: 900;
    color: var(--color-accent);
    line-height: 1;
}

.subscription-plan-card .plan-price .period {
    font-size: 0.9rem;
    color: var(--color-text-secondary);
}

.subscription-plan-card .plan-features {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
    text-align: left;
}

.subscription-plan-card .plan-features li {
    padding: 0.5rem 0;
    color: var(--color-text);
    display: flex;
    align-items: center;
}

.subscription-plan-card .plan-features li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    margin-right: 0.5rem;
}

.subscription-plan-button {
    width: 100%;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.subscription-plan-button.upgrade-btn {
    background: linear-gradient(135deg, var(--color-accent) 0%, #7c3aed 100%);
    color: white;
}

.subscription-plan-button.upgrade-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.subscription-plan-button.current-btn {
    background: #10b981;
    color: white;
    cursor: default;
}

.subscription-plan-button.manage-btn {
    background: var(--color-surface);
    color: var(--color-text);
    border: 2px solid var(--color-border);
}

.subscription-plan-button.manage-btn:hover {
    border-color: var(--color-accent);
    color: var(--color-accent);
}

/* App Footer */
.app-footer {
    background: rgba(15, 23, 42, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    text-align: center;
    margin-top: 3rem;
    width: 100%;
}

.app-footer .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.app-footer p {
    margin: 0;
    color: #94a3b8;
    font-size: 0.9rem;
}
