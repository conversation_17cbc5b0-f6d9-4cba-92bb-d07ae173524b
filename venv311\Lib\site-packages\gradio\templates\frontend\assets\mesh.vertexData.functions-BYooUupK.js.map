{"version": 3, "file": "mesh.vertexData.functions-BYooUupK.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/bitArray.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Meshes/mesh.vertexData.functions.js"], "sourcesContent": ["function getByteIndex(bitIndex) {\n    return Math.floor(bitIndex / 8);\n}\nfunction getBitMask(bitIndex) {\n    return 1 << bitIndex % 8;\n}\n/**\n * An fixed size array that effectively stores boolean values where each value is a single bit of backing data.\n * @remarks\n * All bits are initialized to false.\n */\nexport class BitArray {\n    /**\n     * Creates a new bit array with a fixed size.\n     * @param size The number of bits to store.\n     */\n    constructor(size) {\n        this.size = size;\n        this._byteArray = new Uint8Array(Math.ceil(this.size / 8));\n    }\n    /**\n     * Gets the current value at the specified index.\n     * @param bitIndex The index to get the value from.\n     * @returns The value at the specified index.\n     */\n    get(bitIndex) {\n        if (bitIndex >= this.size) {\n            throw new RangeError(\"Bit index out of range\");\n        }\n        const byteIndex = getByteIndex(bitIndex);\n        const bitMask = getBitMask(bitIndex);\n        return (this._byteArray[byteIndex] & bitMask) !== 0;\n    }\n    /**\n     * Sets the value at the specified index.\n     * @param bitIndex The index to set the value at.\n     * @param value The value to set.\n     */\n    set(bitIndex, value) {\n        if (bitIndex >= this.size) {\n            throw new RangeError(\"Bit index out of range\");\n        }\n        const byteIndex = getByteIndex(bitIndex);\n        const bitMask = getBitMask(bitIndex);\n        if (value) {\n            this._byteArray[byteIndex] |= bitMask;\n        }\n        else {\n            this._byteArray[byteIndex] &= ~bitMask;\n        }\n    }\n}\n//# sourceMappingURL=bitArray.js.map", "import { BitArray } from \"../Misc/bitArray.js\";\n/**\n * Sort (in place) the index array so that faces with common indices are close\n * @param indices the array of indices to sort\n */\nexport function OptimizeIndices(indices) {\n    const faces = [];\n    const faceCount = indices.length / 3;\n    // Step 1: Break the indices array into faces\n    for (let i = 0; i < faceCount; i++) {\n        faces.push([indices[i * 3], indices[i * 3 + 1], indices[i * 3 + 2]]);\n    }\n    // Step 2: Build a graph connecting faces sharing a vertex\n    const vertexToFaceMap = new Map();\n    faces.forEach((face, faceIndex) => {\n        face.forEach((vertex) => {\n            let face = vertexToFaceMap.get(vertex);\n            if (!face) {\n                vertexToFaceMap.set(vertex, (face = []));\n            }\n            face.push(faceIndex);\n        });\n    });\n    // Step 3: Traverse faces using DFS to ensure connected faces are close\n    const visited = new BitArray(faceCount);\n    const sortedFaces = [];\n    // Using a stack and not a recursive version to avoid call stack overflow\n    const deepFirstSearchStack = (startFaceIndex) => {\n        const stack = [startFaceIndex];\n        while (stack.length > 0) {\n            const currentFaceIndex = stack.pop();\n            if (visited.get(currentFaceIndex)) {\n                continue;\n            }\n            visited.set(currentFaceIndex, true);\n            sortedFaces.push(faces[currentFaceIndex]);\n            // Push unvisited neighbors (faces sharing a vertex) onto the stack\n            faces[currentFaceIndex].forEach((vertex) => {\n                const neighbors = vertexToFaceMap.get(vertex);\n                if (!neighbors) {\n                    return;\n                }\n                neighbors.forEach((neighborFaceIndex) => {\n                    if (!visited.get(neighborFaceIndex)) {\n                        stack.push(neighborFaceIndex);\n                    }\n                });\n            });\n        }\n    };\n    // Start DFS from the first face\n    for (let i = 0; i < faceCount; i++) {\n        if (!visited.get(i)) {\n            deepFirstSearchStack(i);\n        }\n    }\n    // Step 4: Flatten the sorted faces back into an array\n    let index = 0;\n    sortedFaces.forEach((face) => {\n        indices[index++] = face[0];\n        indices[index++] = face[1];\n        indices[index++] = face[2];\n    });\n}\n//# sourceMappingURL=mesh.vertexData.functions.js.map"], "names": ["getByteIndex", "bitIndex", "getBitMask", "BitArray", "size", "byteIndex", "bitMask", "value", "OptimizeIndices", "indices", "faces", "faceCount", "i", "vertexToFaceMap", "face", "faceIndex", "vertex", "visited", "sortedFaces", "deepFirstSearchStack", "startFaceIndex", "stack", "currentFaceIndex", "neighbors", "neighborFaceIndex", "index"], "mappings": "AAAA,SAASA,EAAaC,EAAU,CAC5B,OAAO,KAAK,MAAMA,EAAW,CAAC,CAClC,CACA,SAASC,EAAWD,EAAU,CAC1B,MAAO,IAAKA,EAAW,CAC3B,CAMO,MAAME,CAAS,CAKlB,YAAYC,EAAM,CACd,KAAK,KAAOA,EACZ,KAAK,WAAa,IAAI,WAAW,KAAK,KAAK,KAAK,KAAO,CAAC,CAAC,CAC5D,CAMD,IAAIH,EAAU,CACV,GAAIA,GAAY,KAAK,KACjB,MAAM,IAAI,WAAW,wBAAwB,EAEjD,MAAMI,EAAYL,EAAaC,CAAQ,EACjCK,EAAUJ,EAAWD,CAAQ,EACnC,OAAQ,KAAK,WAAWI,CAAS,EAAIC,KAAa,CACrD,CAMD,IAAIL,EAAUM,EAAO,CACjB,GAAIN,GAAY,KAAK,KACjB,MAAM,IAAI,WAAW,wBAAwB,EAEjD,MAAMI,EAAYL,EAAaC,CAAQ,EACjCK,EAAUJ,EAAWD,CAAQ,EAC/BM,EACA,KAAK,WAAWF,CAAS,GAAKC,EAG9B,KAAK,WAAWD,CAAS,GAAK,CAACC,CAEtC,CACL,CC9CO,SAASE,EAAgBC,EAAS,CACrC,MAAMC,EAAQ,CAAA,EACRC,EAAYF,EAAQ,OAAS,EAEnC,QAASG,EAAI,EAAGA,EAAID,EAAWC,IAC3BF,EAAM,KAAK,CAACD,EAAQG,EAAI,CAAC,EAAGH,EAAQG,EAAI,EAAI,CAAC,EAAGH,EAAQG,EAAI,EAAI,CAAC,CAAC,CAAC,EAGvE,MAAMC,EAAkB,IAAI,IAC5BH,EAAM,QAAQ,CAACI,EAAMC,IAAc,CAC/BD,EAAK,QAASE,GAAW,CACrB,IAAIF,EAAOD,EAAgB,IAAIG,CAAM,EAChCF,GACDD,EAAgB,IAAIG,EAASF,EAAO,CAAE,CAAA,EAE1CA,EAAK,KAAKC,CAAS,CAC/B,CAAS,CACT,CAAK,EAED,MAAME,EAAU,IAAId,EAASQ,CAAS,EAChCO,EAAc,CAAA,EAEdC,EAAwBC,GAAmB,CAC7C,MAAMC,EAAQ,CAACD,CAAc,EAC7B,KAAOC,EAAM,OAAS,GAAG,CACrB,MAAMC,EAAmBD,EAAM,MAC3BJ,EAAQ,IAAIK,CAAgB,IAGhCL,EAAQ,IAAIK,EAAkB,EAAI,EAClCJ,EAAY,KAAKR,EAAMY,CAAgB,CAAC,EAExCZ,EAAMY,CAAgB,EAAE,QAASN,GAAW,CACxC,MAAMO,EAAYV,EAAgB,IAAIG,CAAM,EACvCO,GAGLA,EAAU,QAASC,GAAsB,CAChCP,EAAQ,IAAIO,CAAiB,GAC9BH,EAAM,KAAKG,CAAiB,CAEpD,CAAiB,CACjB,CAAa,EACJ,CACT,EAEI,QAASZ,EAAI,EAAGA,EAAID,EAAWC,IACtBK,EAAQ,IAAIL,CAAC,GACdO,EAAqBP,CAAC,EAI9B,IAAIa,EAAQ,EACZP,EAAY,QAASJ,GAAS,CAC1BL,EAAQgB,GAAO,EAAIX,EAAK,CAAC,EACzBL,EAAQgB,GAAO,EAAIX,EAAK,CAAC,EACzBL,EAAQgB,GAAO,EAAIX,EAAK,CAAC,CACjC,CAAK,CACL", "x_google_ignoreList": [0, 1]}