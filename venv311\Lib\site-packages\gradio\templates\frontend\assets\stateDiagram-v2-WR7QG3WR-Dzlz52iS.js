import{s as r,S as e,b as a,a as s}from"./chunk-4IRHCMPZ-DqsKO0Vw.js";import{_ as i}from"./mermaid.core-D-K3Awe9.js";import"./chunk-2O5F6CEG-DK8iCJt_.js";import"./select-BigU4G0v.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./dispatch-kxCwF96_.js";import"./step-Ce-xBr2D.js";var b={parser:r,get db(){return new e(2)},renderer:a,styles:s,init:i(t=>{t.state||(t.state={}),t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute},"init")};export{b as diagram};
//# sourceMappingURL=stateDiagram-v2-WR7QG3WR-Dzlz52iS.js.map
